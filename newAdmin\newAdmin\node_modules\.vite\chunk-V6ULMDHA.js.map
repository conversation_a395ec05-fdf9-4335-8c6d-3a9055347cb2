{"version": 3, "sources": ["../@vue/devtools-api/lib/esm/env.js", "../@vue/devtools-api/lib/esm/const.js", "../@vue/devtools-api/lib/esm/time.js", "../@vue/devtools-api/lib/esm/proxy.js", "../@vue/devtools-api/lib/esm/index.js"], "sourcesContent": ["export function getDevtoolsGlobalHook() {\n    return getTarget().__VUE_DEVTOOLS_GLOBAL_HOOK__;\n}\nexport function getTarget() {\n    // @ts-expect-error navigator and windows are not available in all environments\n    return (typeof navigator !== 'undefined' && typeof window !== 'undefined')\n        ? window\n        : typeof globalThis !== 'undefined'\n            ? globalThis\n            : {};\n}\nexport const isProxyAvailable = typeof Proxy === 'function';\n", "export const HOOK_SETUP = 'devtools-plugin:setup';\nexport const HOOK_PLUGIN_SETTINGS_SET = 'plugin:settings:set';\n", "let supported;\nlet perf;\nexport function isPerformanceSupported() {\n    var _a;\n    if (supported !== undefined) {\n        return supported;\n    }\n    if (typeof window !== 'undefined' && window.performance) {\n        supported = true;\n        perf = window.performance;\n    }\n    else if (typeof globalThis !== 'undefined' && ((_a = globalThis.perf_hooks) === null || _a === void 0 ? void 0 : _a.performance)) {\n        supported = true;\n        perf = globalThis.perf_hooks.performance;\n    }\n    else {\n        supported = false;\n    }\n    return supported;\n}\nexport function now() {\n    return isPerformanceSupported() ? perf.now() : Date.now();\n}\n", "import { HOOK_PLUGIN_SETTINGS_SET } from './const.js';\nimport { now } from './time.js';\nexport class ApiProxy {\n    constructor(plugin, hook) {\n        this.target = null;\n        this.targetQueue = [];\n        this.onQueue = [];\n        this.plugin = plugin;\n        this.hook = hook;\n        const defaultSettings = {};\n        if (plugin.settings) {\n            for (const id in plugin.settings) {\n                const item = plugin.settings[id];\n                defaultSettings[id] = item.defaultValue;\n            }\n        }\n        const localSettingsSaveId = `__vue-devtools-plugin-settings__${plugin.id}`;\n        let currentSettings = Object.assign({}, defaultSettings);\n        try {\n            const raw = localStorage.getItem(localSettingsSaveId);\n            const data = JSON.parse(raw);\n            Object.assign(currentSettings, data);\n        }\n        catch (e) {\n            // noop\n        }\n        this.fallbacks = {\n            getSettings() {\n                return currentSettings;\n            },\n            setSettings(value) {\n                try {\n                    localStorage.setItem(localSettingsSaveId, JSON.stringify(value));\n                }\n                catch (e) {\n                    // noop\n                }\n                currentSettings = value;\n            },\n            now() {\n                return now();\n            },\n        };\n        if (hook) {\n            hook.on(HOOK_PLUGIN_SETTINGS_SET, (pluginId, value) => {\n                if (pluginId === this.plugin.id) {\n                    this.fallbacks.setSettings(value);\n                }\n            });\n        }\n        this.proxiedOn = new Proxy({}, {\n            get: (_target, prop) => {\n                if (this.target) {\n                    return this.target.on[prop];\n                }\n                else {\n                    return (...args) => {\n                        this.onQueue.push({\n                            method: prop,\n                            args,\n                        });\n                    };\n                }\n            },\n        });\n        this.proxiedTarget = new Proxy({}, {\n            get: (_target, prop) => {\n                if (this.target) {\n                    return this.target[prop];\n                }\n                else if (prop === 'on') {\n                    return this.proxiedOn;\n                }\n                else if (Object.keys(this.fallbacks).includes(prop)) {\n                    return (...args) => {\n                        this.targetQueue.push({\n                            method: prop,\n                            args,\n                            resolve: () => { },\n                        });\n                        return this.fallbacks[prop](...args);\n                    };\n                }\n                else {\n                    return (...args) => {\n                        return new Promise((resolve) => {\n                            this.targetQueue.push({\n                                method: prop,\n                                args,\n                                resolve,\n                            });\n                        });\n                    };\n                }\n            },\n        });\n    }\n    async setRealTarget(target) {\n        this.target = target;\n        for (const item of this.onQueue) {\n            this.target.on[item.method](...item.args);\n        }\n        for (const item of this.targetQueue) {\n            item.resolve(await this.target[item.method](...item.args));\n        }\n    }\n}\n", "import { getDevtoolsGlobalHook, getTarget, isProxyAvailable } from './env.js';\nimport { HOOK_SETUP } from './const.js';\nimport { ApiProxy } from './proxy.js';\nexport * from './api/index.js';\nexport * from './plugin.js';\nexport * from './time.js';\nexport function setupDevtoolsPlugin(pluginDescriptor, setupFn) {\n    const descriptor = pluginDescriptor;\n    const target = getTarget();\n    const hook = getDevtoolsGlobalHook();\n    const enableProxy = isProxyAvailable && descriptor.enableEarlyProxy;\n    if (hook && (target.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__ || !enableProxy)) {\n        hook.emit(HOOK_SETUP, pluginDescriptor, setupFn);\n    }\n    else {\n        const proxy = enableProxy ? new ApiProxy(descriptor, hook) : null;\n        const list = target.__VUE_DEVTOOLS_PLUGINS__ = target.__VUE_DEVTOOLS_PLUGINS__ || [];\n        list.push({\n            pluginDescriptor: descriptor,\n            setupFn,\n            proxy,\n        });\n        if (proxy) {\n            setupFn(proxy.proxiedTarget);\n        }\n    }\n}\n"], "mappings": ";AAAO,iCAAiC;AACpC,SAAO,YAAY;AAAA;AAEhB,qBAAqB;AAExB,SAAQ,OAAO,cAAc,eAAe,OAAO,WAAW,cACxD,SACA,OAAO,eAAe,cAClB,aACA;AAAA;AAEP,IAAM,mBAAmB,OAAO,UAAU;;;ACX1C,IAAM,aAAa;AACnB,IAAM,2BAA2B;;;ACDxC,IAAI;AACJ,IAAI;AACG,kCAAkC;AACrC,MAAI;AACJ,MAAI,cAAc,QAAW;AACzB,WAAO;AAAA;AAEX,MAAI,OAAO,WAAW,eAAe,OAAO,aAAa;AACrD,gBAAY;AACZ,WAAO,OAAO;AAAA,aAET,OAAO,eAAe,eAAiB,OAAK,WAAW,gBAAgB,QAAQ,OAAO,SAAS,SAAS,GAAG,cAAc;AAC9H,gBAAY;AACZ,WAAO,WAAW,WAAW;AAAA,SAE5B;AACD,gBAAY;AAAA;AAEhB,SAAO;AAAA;AAEJ,eAAe;AAClB,SAAO,2BAA2B,KAAK,QAAQ,KAAK;AAAA;;;ACnBjD,qBAAe;AAAA,EAClB,YAAY,QAAQ,MAAM;AACtB,SAAK,SAAS;AACd,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,UAAM,kBAAkB;AACxB,QAAI,OAAO,UAAU;AACjB,iBAAW,MAAM,OAAO,UAAU;AAC9B,cAAM,OAAO,OAAO,SAAS;AAC7B,wBAAgB,MAAM,KAAK;AAAA;AAAA;AAGnC,UAAM,sBAAsB,mCAAmC,OAAO;AACtE,QAAI,kBAAkB,OAAO,OAAO,IAAI;AACxC,QAAI;AACA,YAAM,MAAM,aAAa,QAAQ;AACjC,YAAM,OAAO,KAAK,MAAM;AACxB,aAAO,OAAO,iBAAiB;AAAA,aAE5B,GAAP;AAAA;AAGA,SAAK,YAAY;AAAA,MACb,cAAc;AACV,eAAO;AAAA;AAAA,MAEX,YAAY,OAAO;AACf,YAAI;AACA,uBAAa,QAAQ,qBAAqB,KAAK,UAAU;AAAA,iBAEtD,GAAP;AAAA;AAGA,0BAAkB;AAAA;AAAA,MAEtB,MAAM;AACF,eAAO;AAAA;AAAA;AAGf,QAAI,MAAM;AACN,WAAK,GAAG,0BAA0B,CAAC,UAAU,UAAU;AACnD,YAAI,aAAa,KAAK,OAAO,IAAI;AAC7B,eAAK,UAAU,YAAY;AAAA;AAAA;AAAA;AAIvC,SAAK,YAAY,IAAI,MAAM,IAAI;AAAA,MAC3B,KAAK,CAAC,SAAS,SAAS;AACpB,YAAI,KAAK,QAAQ;AACb,iBAAO,KAAK,OAAO,GAAG;AAAA,eAErB;AACD,iBAAO,IAAI,SAAS;AAChB,iBAAK,QAAQ,KAAK;AAAA,cACd,QAAQ;AAAA,cACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAMpB,SAAK,gBAAgB,IAAI,MAAM,IAAI;AAAA,MAC/B,KAAK,CAAC,SAAS,SAAS;AACpB,YAAI,KAAK,QAAQ;AACb,iBAAO,KAAK,OAAO;AAAA,mBAEd,SAAS,MAAM;AACpB,iBAAO,KAAK;AAAA,mBAEP,OAAO,KAAK,KAAK,WAAW,SAAS,OAAO;AACjD,iBAAO,IAAI,SAAS;AAChB,iBAAK,YAAY,KAAK;AAAA,cAClB,QAAQ;AAAA,cACR;AAAA,cACA,SAAS,MAAM;AAAA;AAAA;AAEnB,mBAAO,KAAK,UAAU,MAAM,GAAG;AAAA;AAAA,eAGlC;AACD,iBAAO,IAAI,SAAS;AAChB,mBAAO,IAAI,QAAQ,CAAC,YAAY;AAC5B,mBAAK,YAAY,KAAK;AAAA,gBAClB,QAAQ;AAAA,gBACR;AAAA,gBACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAQtB,cAAc,QAAQ;AACxB,SAAK,SAAS;AACd,eAAW,QAAQ,KAAK,SAAS;AAC7B,WAAK,OAAO,GAAG,KAAK,QAAQ,GAAG,KAAK;AAAA;AAExC,eAAW,QAAQ,KAAK,aAAa;AACjC,WAAK,QAAQ,MAAM,KAAK,OAAO,KAAK,QAAQ,GAAG,KAAK;AAAA;AAAA;AAAA;;;ACjGzD,6BAA6B,kBAAkB,SAAS;AAC3D,QAAM,aAAa;AACnB,QAAM,SAAS;AACf,QAAM,OAAO;AACb,QAAM,cAAc,oBAAoB,WAAW;AACnD,MAAI,QAAS,QAAO,yCAAyC,CAAC,cAAc;AACxE,SAAK,KAAK,YAAY,kBAAkB;AAAA,SAEvC;AACD,UAAM,QAAQ,cAAc,IAAI,SAAS,YAAY,QAAQ;AAC7D,UAAM,OAAO,OAAO,2BAA2B,OAAO,4BAA4B;AAClF,SAAK,KAAK;AAAA,MACN,kBAAkB;AAAA,MAClB;AAAA,MACA;AAAA;AAEJ,QAAI,OAAO;AACP,cAAQ,MAAM;AAAA;AAAA;AAAA;", "names": []}