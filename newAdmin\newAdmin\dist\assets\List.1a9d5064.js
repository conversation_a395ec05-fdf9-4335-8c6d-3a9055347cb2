var e=Object.defineProperty,a=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,t=Object.prototype.propertyIsEnumerable,o=(a,l,t)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:t}):a[l]=t;import{u as r,ab as d,A as s,C as n,aq as u,ac as i,a6 as c,af as p,ah as m,aM as v,ai as f,aj as h,ad as y,aC as b,aD as g,ae as w,ap as C,ak as k,al as _,au as V,o as j,k as I,l as x,m as D,w as U,v as N,n as O,aw as E,as as $,ay as z,s as S,av as P,H as q,t as A,J as B,aN as L,aO as R,L as T,I as H,a9 as J,aa as M,E as F}from"./vendor.db8f8423.js";/* empty css                   *//* empty css                    *//* empty css                  *//* empty css                   *//* empty css                     *//* empty css                 *//* empty css                      *//* empty css                 *//* empty css                        */import{i as G}from"./index.2f9d9633.js";const K=e=>G.get("/depot/list",{params:e}),Q=e=>G.post("/depot/create",e),W=e=>G.put("/depot/update",e),X=e=>G.delete(`/depot/delete/${e}`),Y=e=>G.get(`/depot/shelves/${e}`),Z=e=>G.get(`/depot/freight/${e}`);const ee=e=>(J("data-v-fa177cf4"),e=e(),M(),e),ae={class:"container"},le={class:"search-area"},te={class:"search-row"},oe={class:"search-item"},re=ee((()=>x("span",{class:"search-label"},"一级货区编号",-1))),de={class:"search-item"},se=ee((()=>x("span",{class:"search-label"},"一级货区名称",-1))),ne={class:"search-item btn-item"},ue={class:"action-bar"},ie={class:"action-left"},ce={class:"template-select"},pe={key:0},me={key:1,class:"template-link"},ve={class:"pagination-container"},fe={class:"dialog-footer"},he={class:"dialog-footer"},ye={__name:"List",setup(e){r();const J=d({}),M=s([]),G=s(!1),ee=s(0),ye=s(1),be=s(10),ge=s([]),we=s(!1),Ce=s("add"),ke=s(null),_e=d({id:null,name:"",code:"",categoryNumber:"",userId:"",status:0}),Ve=s(!1),je=d({depotId:null,templateId:null}),Ie=s([{value:"1",label:"南宁"},{value:"2",label:"南门坡街"}]),xe=s(null),De=d({name:[{required:!0,message:"请输入货区名称",trigger:"blur"}],code:[{required:!0,message:"请输入货区编号",trigger:"blur"}]}),Ue=async()=>{try{const e=Ie.value.find((e=>e.value===je.templateId));xe.value&&e?(xe.value.templateName=e.label,u.success("运费模板设置成功"),Ve.value=!1):u.error("请选择运费模板")}catch(e){console.error("设置运费模板失败",e),u.error("设置运费模板失败")}};n((()=>{Ne()}));const Ne=async()=>{G.value=!0;try{const e=((e,r)=>{for(var d in r||(r={}))l.call(r,d)&&o(e,d,r[d]);if(a)for(var d of a(r))t.call(r,d)&&o(e,d,r[d]);return e})({pageNum:ye.value,pageSize:be.value},J),r=await K(e);if(200===r.code){const e=r.data.list||[];ee.value=r.data.total||0,e.forEach((e=>{e.hasChildren=!0,e.level=1})),M.value=e}else u.error(r.message||"获取货区数据失败")}catch(e){console.error("获取货区数据失败",e),u.error("获取货区数据失败")}finally{G.value=!1}},Oe=()=>{ye.value=1,Ne()},Ee=()=>{Object.keys(J).forEach((e=>{J[e]=void 0})),ye.value=1,Ne()},$e=()=>{Ce.value="add",Object.keys(_e).forEach((e=>{_e[e]="status"===e?0:""})),we.value=!0},ze=()=>{const e=document.createElement("a");e.href="/templates/depotRule.doc",e.download="仓库规则设置.doc",document.body.appendChild(e),e.click(),document.body.removeChild(e)},Se=async()=>{ke.value&&await ke.value.validate((async e=>{if(e)try{let e;e="add"===Ce.value?await Q(_e):await W(_e),200===e.code?(u.success("add"===Ce.value?"创建成功":"更新成功"),we.value=!1,Ne()):u.error(e.message||("add"===Ce.value?"创建失败":"更新失败"))}catch(a){console.error("add"===Ce.value?"创建失败":"更新失败",a),u.error("add"===Ce.value?"创建失败":"更新失败")}}))},Pe=e=>{ge.value=e},qe=e=>{be.value=e,Ne()},Ae=e=>{ye.value=e,Ne()},Be=async(e,a,l)=>{if(1===e.level)try{const a=await Y(e.id);if(200===a.code){const t=a.data||[];0===t.length?l([{id:`empty-${e.id}`,name:"暂无数据",noData:!0,hasChildren:!1}]):(t.forEach((e=>{e.hasChildren=!0,e.level=2})),l(t))}else l([{id:`empty-${e.id}`,name:"暂无数据",noData:!0,hasChildren:!1}]),u.error(a.message||"获取二级货架数据失败")}catch(t){console.error("获取二级货架数据失败",t),u.error("获取二级货架数据失败"),l([{id:`empty-${e.id}`,name:"暂无数据",noData:!0,hasChildren:!1}])}else if(2===e.level)try{const a=await Z(e.id);if(200===a.code){const t=a.data||[];0===t.length?l([{id:`empty-${e.id}`,name:"暂无数据",noData:!0,hasChildren:!1}]):(t.forEach((e=>{e.level=3,e.hasChildren=!1})),l(t))}else l([{id:`empty-${e.id}`,name:"暂无数据",noData:!0,hasChildren:!1}]),u.error(a.message||"获取三级货位数据失败")}catch(t){console.error("获取三级货位数据失败",t),u.error("获取三级货位数据失败"),l([{id:`empty-${e.id}`,name:"暂无数据",noData:!0,hasChildren:!1}])}else l([])},Le=({row:e})=>e.noData?"empty-row":"";return(e,a)=>{const l=i,t=c,o=p,r=m,d=v,s=f,n=h,K=y,Q=b,W=g,Y=w,Z=C,ge=k,Re=_,Te=V;return j(),I("div",ae,[x("div",le,[x("div",te,[x("div",oe,[re,D(l,{modelValue:J.code,"onUpdate:modelValue":a[0]||(a[0]=e=>J.code=e),placeholder:"请输入一级货区编号",clearable:""},null,8,["modelValue"])]),x("div",de,[se,D(l,{modelValue:J.name,"onUpdate:modelValue":a[1]||(a[1]=e=>J.name=e),placeholder:"请输入名称",clearable:""},null,8,["modelValue"])]),x("div",ne,[D(t,{type:"primary",onClick:Oe,icon:O(E)},{default:U((()=>[N("搜索")])),_:1},8,["icon"]),D(t,{onClick:Ee,icon:O($)},{default:U((()=>[N("重置")])),_:1},8,["icon"])])])]),x("div",ue,[x("div",ie,[D(t,{type:"primary",onClick:$e,icon:O(z)},{default:U((()=>[N("创建一级货区")])),_:1},8,["icon"]),D(t,{onClick:ze,icon:O(S)},{default:U((()=>[N("仓库规则设置")])),_:1},8,["icon"])])]),P((j(),q(s,{data:M.value,"row-key":"id",border:"",lazy:"",load:Be,"tree-props":{children:"children",hasChildren:"hasChildren"},onSelectionChange:Pe,"empty-text":"暂无数据",stripe:"","highlight-current-row":"","header-cell-style":{backgroundColor:"#f5f7fa",color:"#606266",textAlign:"center"},"row-class-name":Le,height:"500","max-height":"500"},{default:U((()=>[D(o,{type:"selection",width:"55"}),D(o,{label:"货区名称"},{default:U((e=>[x("span",null,A(e.row.name)+A(e.row.unit?" ("+e.row.unit+")":""),1)])),_:1}),D(o,{prop:"code",label:"货区编号"}),D(o,{prop:"categoryNumber",label:"书品类别"}),D(o,{prop:"inventory",label:"库存数量"}),D(o,{prop:"userId",label:"用户"}),D(o,{prop:"status",label:"货区状态"},{default:U((e=>[D(r,{type:"0"===e.row.status||0===e.row.status?"success":"danger"},{default:U((()=>[N(A("0"===e.row.status||0===e.row.status?"正常":"异常(未选择运费模板)"),1)])),_:2},1032,["type"])])),_:1}),D(o,{label:"运费模板选择",width:"150"},{default:U((e=>[x("div",ce,[e.row.templateName?(j(),I("span",pe,A(e.row.templateName),1)):(j(),I("span",me,[D(d,{type:"primary",onClick:a=>{return l=e.row,xe.value=l,je.depotId=l.id,je.templateId=null,void(Ve.value=!0);var l}},{default:U((()=>[N("未选择")])),_:2},1032,["onClick"])]))])])),_:1}),D(o,{label:"操作",width:"150"},{default:U((e=>[e.row.level&&1!==e.row.level?T("",!0):(j(),I(B,{key:0},[D(t,{type:"primary",link:"",icon:O(z),onClick:a=>(e.row,void u.info("添加运费模板功能待实现"))},null,8,["icon","onClick"]),D(t,{type:"primary",link:"",icon:O(L),onClick:a=>{return l=e.row,Ce.value="edit",Object.keys(_e).forEach((e=>{_e[e]=l[e]})),void(we.value=!0);var l}},null,8,["icon","onClick"]),e.row.id&&!e.row.noData?(j(),q(t,{key:0,type:"danger",link:"",icon:O(R),onClick:a=>{var l;(l=e.row).id?F.confirm("确认删除该货区?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{var e,a;try{if(console.log(l),!l.id||"null"===l.id||l.noData)return void u.error("无效的ID，无法删除");const e=await X(l.id);200===e.code?(u.success("删除成功"),Ne()):u.error(e.message||"删除失败")}catch(t){console.error("删除失败",t),u.error("删除失败: "+((null==(a=null==(e=t.response)?void 0:e.data)?void 0:a.message)||t.message||"未知错误"))}})).catch((()=>{})):u.error("无效的ID，无法删除")}},null,8,["icon","onClick"])):T("",!0)],64))])),_:1})])),_:1},8,["data"])),[[Te,G.value]]),x("div",ve,[D(n,{"current-page":ye.value,"onUpdate:currentPage":a[2]||(a[2]=e=>ye.value=e),"page-size":be.value,"onUpdate:pageSize":a[3]||(a[3]=e=>be.value=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:ee.value,onSizeChange:qe,onCurrentChange:Ae},null,8,["current-page","page-size","total"])]),D(Z,{modelValue:we.value,"onUpdate:modelValue":a[10]||(a[10]=e=>we.value=e),title:"add"===Ce.value?"创建货区":"编辑货区",width:"500px"},{footer:U((()=>[x("span",fe,[D(t,{onClick:a[9]||(a[9]=e=>we.value=!1)},{default:U((()=>[N("取消")])),_:1}),D(t,{type:"primary",onClick:Se},{default:U((()=>[N("确定")])),_:1})])])),default:U((()=>[D(Y,{model:_e,"label-width":"120px",rules:De,ref_key:"formRef",ref:ke},{default:U((()=>[D(K,{label:"货区名称",prop:"name"},{default:U((()=>[D(l,{modelValue:_e.name,"onUpdate:modelValue":a[4]||(a[4]=e=>_e.name=e),placeholder:"请输入货区名称"},null,8,["modelValue"])])),_:1}),D(K,{label:"货区编号",prop:"code"},{default:U((()=>[D(l,{modelValue:_e.code,"onUpdate:modelValue":a[5]||(a[5]=e=>_e.code=e),placeholder:"请输入货区编号"},null,8,["modelValue"])])),_:1}),D(K,{label:"书品类别",prop:"categoryNumber"},{default:U((()=>[D(l,{modelValue:_e.categoryNumber,"onUpdate:modelValue":a[6]||(a[6]=e=>_e.categoryNumber=e),placeholder:"请输入书品类别"},null,8,["modelValue"])])),_:1}),D(K,{label:"用户",prop:"userId"},{default:U((()=>[D(l,{modelValue:_e.userId,"onUpdate:modelValue":a[7]||(a[7]=e=>_e.userId=e),placeholder:"请输入用户ID"},null,8,["modelValue"])])),_:1}),D(K,{label:"货区状态",prop:"status"},{default:U((()=>[D(W,{modelValue:_e.status,"onUpdate:modelValue":a[8]||(a[8]=e=>_e.status=e)},{default:U((()=>[D(Q,{label:0},{default:U((()=>[N("正常")])),_:1}),D(Q,{label:1},{default:U((()=>[N("异常")])),_:1})])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"]),D(Z,{modelValue:Ve.value,"onUpdate:modelValue":a[13]||(a[13]=e=>Ve.value=e),title:"选择运费模板",width:"500px"},{footer:U((()=>[x("span",he,[D(t,{onClick:a[12]||(a[12]=e=>Ve.value=!1)},{default:U((()=>[N("取消")])),_:1}),D(t,{type:"primary",onClick:Ue},{default:U((()=>[N("确定")])),_:1})])])),default:U((()=>[D(Y,{model:je,"label-width":"120px"},{default:U((()=>[D(K,{label:"运费模板"},{default:U((()=>[D(Re,{modelValue:je.templateId,"onUpdate:modelValue":a[11]||(a[11]=e=>je.templateId=e),placeholder:"请选择运费模板"},{default:U((()=>[(j(!0),I(B,null,H(Ie.value,(e=>(j(),q(ge,{key:e.value,label:e.label,value:e.value},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])])}},__scopeId:"data-v-fa177cf4"};export{ye as default};
