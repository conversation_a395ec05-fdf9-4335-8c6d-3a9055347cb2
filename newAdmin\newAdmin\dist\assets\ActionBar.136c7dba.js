import{R as s}from"./RefreshButton.54bbaca3.js";import{o as a,k as e,l as t,ar as o,H as r,L as i}from"./vendor.db8f8423.js";const n={class:"action-bar"},c={class:"action-left"},f={class:"action-right"},h=Object.assign({name:"ActionBar"},{__name:"ActionBar",props:{showRefresh:{type:Boolean,default:!0}},emits:["refresh"],setup(h,{emit:l}){const d=l,m=()=>{d("refresh")};return(l,d)=>(a(),e("div",n,[t("div",c,[o(l.$slots,"left",{},void 0,!0)]),t("div",f,[o(l.$slots,"right",{},(()=>[h.showRefresh?(a(),r(s,{key:0,onRefresh:m})):i("",!0)]),!0)])]))}});h.__scopeId="data-v-01186ab0";var l=h;export{l as A};
