# 需求文档

## 介绍

在redirectUrl页面中创建一个"点击跳转"按钮，用户点击后跳转到ERP注册页面。跳转URL需要动态构建，包含pddMallId、pddMallName、accessToken和skuSpec等参数。

## 需求

### 需求 1

**用户故事：** 作为用户，我希望能够通过点击"点击跳转"按钮跳转到ERP注册页面，以便我可以完成注册流程。

#### 验收标准

1. 当用户访问redirectUrl页面时，系统应该显示一个"点击跳转"按钮
2. 当用户点击跳转按钮时，系统应该构建完整的跳转URL并在新标签页中打开
3. 当构建URL时，系统应该正确编码pddMallName和skuSpec参数
4. 当跳转URL构建完成时，系统应该包含所有必需的参数：pddMallId、pddMallName、type、accessToken、skuSpec

### 需求 2

**用户故事：** 作为用户，我希望页面有良好的视觉设计，以便我能够清楚地理解页面的用途。

#### 验收标准

1. 当页面加载时，系统应该显示清晰的页面标题
2. 当页面显示时，系统应该使用Element Plus组件库提供美观的按钮样式
3. 当用户悬停在跳转按钮上时，系统应该提供视觉反馈

### 需求 3

**用户故事：** 作为开发者，我希望能够灵活配置跳转参数，以便在不同场景下使用不同的参数值。

#### 验收标准

1. 当页面初始化时，系统应该能够获取或设置pddMallId、pddMallName、accessToken、skuSpec参数
2. 当参数缺失时，系统应该使用默认值或显示适当的提示
3. 当URL构建时，系统应该正确处理中文字符编码