import{d as m,g as e,A as a,a6 as t,a7 as r,a8 as n,o as s,k as T,m as o,w as _,l as c,t as d,n as l,v as i,a9 as E,aa as I}from"./vendor.db8f8423.js";/* empty css                *//* empty css               */let p=null;const D={receipt:(m,e)=>{m.PRINT_INIT("销售小票"),m.SET_PRINT_PAGESIZE(1,"76mm","110mm",""),m.ADD_PRINT_TEXT("10mm","5mm","60mm","5mm","=== 销售小票 ==="),m.SET_PRINT_STYLEA(0,"FontSize",12),e.items.forEach(((e,a)=>{m.ADD_PRINT_TEXT(25+8*a+"mm","5mm","60mm","5mm",`${e.name} x${e.qty} ￥${e.price}`)}))},yunda:(m,e)=>{m.PRINT_INIT("韵达快递单"),m.SET_PRINT_PAGESIZE(3,"100mm","150mm",""),m.ADD_PRINT_RECT("3mm","3mm","94mm","144mm",0,1),m.ADD_PRINT_LINE("20mm","3mm","20mm","97mm",0,1),m.ADD_PRINT_LINE("75mm","3mm","75mm","97mm",0,1),m.ADD_PRINT_LINE("120mm","3mm","120mm","97mm",0,1),m.ADD_PRINT_TEXT("5mm","65mm","30mm","8mm","代收货款"),m.ADD_PRINT_TEXT("10mm","65mm","30mm","8mm",`¥${e.payment}`),m.SET_PRINT_STYLEA(0,"FontSize",10),m.ADD_PRINT_TEXT("15mm","65mm","30mm","5mm","运费总计"),m.ADD_PRINT_TEXT("20mm","65mm","30mm","5mm",`¥${e.freight}`),m.SET_PRINT_STYLEA(0,"FontSize",8),m.ADD_PRINT_BARCODE("25mm","10mm","60mm","15mm","CODE39",e.trackingNumber),m.ADD_PRINT_TEXT("40mm","10mm","60mm","5mm",e.trackingNumber),m.ADD_PRINT_TEXT("45mm","10mm","40mm","5mm",e.cityCode),m.SET_PRINT_STYLEA(0,"FontSize",9),m.ADD_PRINT_TEXT("50mm","10mm","30mm","5mm",`集 ${e.packageCenter}`),m.ADD_PRINT_TEXT("80mm","5mm","40mm","25mm",`联 ${e.receiver.name}\n ${e.receiver.phone}\n${e.receiver.address}`),m.SET_PRINT_STYLEA(0,"LineSpacing","3mm"),m.ADD_PRINT_TEXT("80mm","50mm","40mm","25mm",`寄 ${e.sender.name}\n ${e.sender.phone}\n${e.sender.address}`),m.ADD_PRINT_TEXT("125mm","10mm","80mm","5mm","已验视"),m.ADD_PRINT_TEXT("135mm","10mm","80mm","5mm","本包裹由大商道商贸技术支持"),m.SET_PRINT_STYLEA(0,"Bold",1),m.SET_PRINT_STYLEA(0,"Alignment",2)}},N=async(m,e)=>{try{const a=await new Promise(((m,e)=>{if(p)return m(p);const a=document.createElement("script");a.src="http://localhost:8000/CLodopfuncs.js",a.onload=()=>{p=getCLodop(),p||e(new Error("C-Lodop 未正确安装")),m(p)},a.onerror=()=>e(new Error("加载 C-Lodop 失败")),document.head.appendChild(a)}));if(!D[m])throw console.log(m),new Error(`未找到模板: ${m}`);return D[m](a,e),a}catch(a){throw a}},P={class:"welcome-container"},R={class:"content-wrapper"},A={class:"welcome-title"},u=(m=>(E("data-v-8a731d3e"),m=m(),I(),m))((()=>c("p",{class:"sub-text"},"让管理更高效，让工作更轻松",-1)));var S=m({__name:"Index",setup(m){var E;const I=null==(E=e())?void 0:E.appContext.config.globalProperties.$global;a([]),a(!1);const p={payment:100,freight:8.39,cityCode:"杭州 123A-456-789",packageCenter:"杭州",sender:{name:"张三",phone:"13800138000",address:"上海市浦东新区韵达总部大厦1号楼"},receiver:{name:"李四",phone:"13900139000",address:"北京市海淀区中关村大街1号院5号楼"},trackingNumber:"*************",weight:1.5,remark:"易碎品，轻拿轻放"},D=async(m=!0)=>{try{const e=await N("yunda",p);m?e.PREVIEW():e.PRINT()}catch(e){alert(e.message)}};return(m,e)=>{const a=t,E=r,p=n;return s(),T("div",P,[o(p,{class:"welcome-card"},{default:_((()=>{var m;return[c("div",R,[c("h1",A,"欢迎使用"+d(null==(m=l(I))?void 0:m.system.name)+"管理系统",1),u,o(a,{type:"primary",class:"start-button",onClick:e[0]||(e[0]=()=>{})},{default:_((()=>[i("立即开始使用")])),_:1}),o(E,{style:{"margin-top":"20px"}},{default:_((()=>[o(a,{type:"primary",class:"start-button",onClick:e[1]||(e[1]=m=>D(!0))},{default:_((()=>[i("打印预览")])),_:1})])),_:1}),o(E,{style:{"margin-top":"20px"}},{default:_((()=>[o(a,{type:"primary",class:"start-button",onClick:e[2]||(e[2]=m=>D(!1))},{default:_((()=>[i("直接打印")])),_:1})])),_:1})])]})),_:1})])}}});S.__scopeId="data-v-8a731d3e";export{S as default};
