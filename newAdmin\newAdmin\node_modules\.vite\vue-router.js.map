{"version": 3, "sources": ["../vue-router/dist/vue-router.mjs"], "sourcesContent": ["/*!\n  * vue-router v4.5.1\n  * (c) 2025 <PERSON>\n  * @license MIT\n  */\nimport { getCurrentInstance, inject, onUnmounted, onDeactivated, onActivated, computed, unref, watchEffect, defineComponent, reactive, h, provide, ref, watch, shallowRef, shallowReactive, nextTick } from 'vue';\nimport { setupDevtoolsPlugin } from '@vue/devtools-api';\n\nconst isBrowser = typeof document !== 'undefined';\n\n/**\n * Allows differentiating lazy components from functional components and vue-class-component\n * @internal\n *\n * @param component\n */\nfunction isRouteComponent(component) {\n    return (typeof component === 'object' ||\n        'displayName' in component ||\n        'props' in component ||\n        '__vccOpts' in component);\n}\nfunction isESModule(obj) {\n    return (obj.__esModule ||\n        obj[Symbol.toStringTag] === 'Module' ||\n        // support CF with dynamic imports that do not\n        // add the Module string tag\n        (obj.default && isRouteComponent(obj.default)));\n}\nconst assign = Object.assign;\nfunction applyToParams(fn, params) {\n    const newParams = {};\n    for (const key in params) {\n        const value = params[key];\n        newParams[key] = isArray(value)\n            ? value.map(fn)\n            : fn(value);\n    }\n    return newParams;\n}\nconst noop = () => { };\n/**\n * Typesafe alternative to Array.isArray\n * https://github.com/microsoft/TypeScript/pull/48228\n */\nconst isArray = Array.isArray;\n\nfunction warn(msg) {\n    // avoid using ...args as it breaks in older Edge builds\n    const args = Array.from(arguments).slice(1);\n    console.warn.apply(console, ['[Vue Router warn]: ' + msg].concat(args));\n}\n\n/**\n * Encoding Rules (␣ = Space)\n * - Path: ␣ \" < > # ? { }\n * - Query: ␣ \" < > # & =\n * - Hash: ␣ \" < > `\n *\n * On top of that, the RFC3986 (https://tools.ietf.org/html/rfc3986#section-2.2)\n * defines some extra characters to be encoded. Most browsers do not encode them\n * in encodeURI https://github.com/whatwg/url/issues/369, so it may be safer to\n * also encode `!'()*`. Leaving un-encoded only ASCII alphanumeric(`a-zA-Z0-9`)\n * plus `-._~`. This extra safety should be applied to query by patching the\n * string returned by encodeURIComponent encodeURI also encodes `[\\]^`. `\\`\n * should be encoded to avoid ambiguity. Browsers (IE, FF, C) transform a `\\`\n * into a `/` if directly typed in. The _backtick_ (`````) should also be\n * encoded everywhere because some browsers like FF encode it when directly\n * written while others don't. Safari and IE don't encode ``\"<>{}``` in hash.\n */\n// const EXTRA_RESERVED_RE = /[!'()*]/g\n// const encodeReservedReplacer = (c: string) => '%' + c.charCodeAt(0).toString(16)\nconst HASH_RE = /#/g; // %23\nconst AMPERSAND_RE = /&/g; // %26\nconst SLASH_RE = /\\//g; // %2F\nconst EQUAL_RE = /=/g; // %3D\nconst IM_RE = /\\?/g; // %3F\nconst PLUS_RE = /\\+/g; // %2B\n/**\n * NOTE: It's not clear to me if we should encode the + symbol in queries, it\n * seems to be less flexible than not doing so and I can't find out the legacy\n * systems requiring this for regular requests like text/html. In the standard,\n * the encoding of the plus character is only mentioned for\n * application/x-www-form-urlencoded\n * (https://url.spec.whatwg.org/#urlencoded-parsing) and most browsers seems lo\n * leave the plus character as is in queries. To be more flexible, we allow the\n * plus character on the query, but it can also be manually encoded by the user.\n *\n * Resources:\n * - https://url.spec.whatwg.org/#urlencoded-parsing\n * - https://stackoverflow.com/questions/1634271/url-encoding-the-space-character-or-20\n */\nconst ENC_BRACKET_OPEN_RE = /%5B/g; // [\nconst ENC_BRACKET_CLOSE_RE = /%5D/g; // ]\nconst ENC_CARET_RE = /%5E/g; // ^\nconst ENC_BACKTICK_RE = /%60/g; // `\nconst ENC_CURLY_OPEN_RE = /%7B/g; // {\nconst ENC_PIPE_RE = /%7C/g; // |\nconst ENC_CURLY_CLOSE_RE = /%7D/g; // }\nconst ENC_SPACE_RE = /%20/g; // }\n/**\n * Encode characters that need to be encoded on the path, search and hash\n * sections of the URL.\n *\n * @internal\n * @param text - string to encode\n * @returns encoded string\n */\nfunction commonEncode(text) {\n    return encodeURI('' + text)\n        .replace(ENC_PIPE_RE, '|')\n        .replace(ENC_BRACKET_OPEN_RE, '[')\n        .replace(ENC_BRACKET_CLOSE_RE, ']');\n}\n/**\n * Encode characters that need to be encoded on the hash section of the URL.\n *\n * @param text - string to encode\n * @returns encoded string\n */\nfunction encodeHash(text) {\n    return commonEncode(text)\n        .replace(ENC_CURLY_OPEN_RE, '{')\n        .replace(ENC_CURLY_CLOSE_RE, '}')\n        .replace(ENC_CARET_RE, '^');\n}\n/**\n * Encode characters that need to be encoded query values on the query\n * section of the URL.\n *\n * @param text - string to encode\n * @returns encoded string\n */\nfunction encodeQueryValue(text) {\n    return (commonEncode(text)\n        // Encode the space as +, encode the + to differentiate it from the space\n        .replace(PLUS_RE, '%2B')\n        .replace(ENC_SPACE_RE, '+')\n        .replace(HASH_RE, '%23')\n        .replace(AMPERSAND_RE, '%26')\n        .replace(ENC_BACKTICK_RE, '`')\n        .replace(ENC_CURLY_OPEN_RE, '{')\n        .replace(ENC_CURLY_CLOSE_RE, '}')\n        .replace(ENC_CARET_RE, '^'));\n}\n/**\n * Like `encodeQueryValue` but also encodes the `=` character.\n *\n * @param text - string to encode\n */\nfunction encodeQueryKey(text) {\n    return encodeQueryValue(text).replace(EQUAL_RE, '%3D');\n}\n/**\n * Encode characters that need to be encoded on the path section of the URL.\n *\n * @param text - string to encode\n * @returns encoded string\n */\nfunction encodePath(text) {\n    return commonEncode(text).replace(HASH_RE, '%23').replace(IM_RE, '%3F');\n}\n/**\n * Encode characters that need to be encoded on the path section of the URL as a\n * param. This function encodes everything {@link encodePath} does plus the\n * slash (`/`) character. If `text` is `null` or `undefined`, returns an empty\n * string instead.\n *\n * @param text - string to encode\n * @returns encoded string\n */\nfunction encodeParam(text) {\n    return text == null ? '' : encodePath(text).replace(SLASH_RE, '%2F');\n}\n/**\n * Decode text using `decodeURIComponent`. Returns the original text if it\n * fails.\n *\n * @param text - string to decode\n * @returns decoded string\n */\nfunction decode(text) {\n    try {\n        return decodeURIComponent('' + text);\n    }\n    catch (err) {\n        (process.env.NODE_ENV !== 'production') && warn(`Error decoding \"${text}\". Using original value`);\n    }\n    return '' + text;\n}\n\nconst TRAILING_SLASH_RE = /\\/$/;\nconst removeTrailingSlash = (path) => path.replace(TRAILING_SLASH_RE, '');\n/**\n * Transforms a URI into a normalized history location\n *\n * @param parseQuery\n * @param location - URI to normalize\n * @param currentLocation - current absolute location. Allows resolving relative\n * paths. Must start with `/`. Defaults to `/`\n * @returns a normalized history location\n */\nfunction parseURL(parseQuery, location, currentLocation = '/') {\n    let path, query = {}, searchString = '', hash = '';\n    // Could use URL and URLSearchParams but IE 11 doesn't support it\n    // TODO: move to new URL()\n    const hashPos = location.indexOf('#');\n    let searchPos = location.indexOf('?');\n    // the hash appears before the search, so it's not part of the search string\n    if (hashPos < searchPos && hashPos >= 0) {\n        searchPos = -1;\n    }\n    if (searchPos > -1) {\n        path = location.slice(0, searchPos);\n        searchString = location.slice(searchPos + 1, hashPos > -1 ? hashPos : location.length);\n        query = parseQuery(searchString);\n    }\n    if (hashPos > -1) {\n        path = path || location.slice(0, hashPos);\n        // keep the # character\n        hash = location.slice(hashPos, location.length);\n    }\n    // no search and no query\n    path = resolveRelativePath(path != null ? path : location, currentLocation);\n    // empty path means a relative query or hash `?foo=f`, `#thing`\n    return {\n        fullPath: path + (searchString && '?') + searchString + hash,\n        path,\n        query,\n        hash: decode(hash),\n    };\n}\n/**\n * Stringifies a URL object\n *\n * @param stringifyQuery\n * @param location\n */\nfunction stringifyURL(stringifyQuery, location) {\n    const query = location.query ? stringifyQuery(location.query) : '';\n    return location.path + (query && '?') + query + (location.hash || '');\n}\n/**\n * Strips off the base from the beginning of a location.pathname in a non-case-sensitive way.\n *\n * @param pathname - location.pathname\n * @param base - base to strip off\n */\nfunction stripBase(pathname, base) {\n    // no base or base is not found at the beginning\n    if (!base || !pathname.toLowerCase().startsWith(base.toLowerCase()))\n        return pathname;\n    return pathname.slice(base.length) || '/';\n}\n/**\n * Checks if two RouteLocation are equal. This means that both locations are\n * pointing towards the same {@link RouteRecord} and that all `params`, `query`\n * parameters and `hash` are the same\n *\n * @param stringifyQuery - A function that takes a query object of type LocationQueryRaw and returns a string representation of it.\n * @param a - first {@link RouteLocation}\n * @param b - second {@link RouteLocation}\n */\nfunction isSameRouteLocation(stringifyQuery, a, b) {\n    const aLastIndex = a.matched.length - 1;\n    const bLastIndex = b.matched.length - 1;\n    return (aLastIndex > -1 &&\n        aLastIndex === bLastIndex &&\n        isSameRouteRecord(a.matched[aLastIndex], b.matched[bLastIndex]) &&\n        isSameRouteLocationParams(a.params, b.params) &&\n        stringifyQuery(a.query) === stringifyQuery(b.query) &&\n        a.hash === b.hash);\n}\n/**\n * Check if two `RouteRecords` are equal. Takes into account aliases: they are\n * considered equal to the `RouteRecord` they are aliasing.\n *\n * @param a - first {@link RouteRecord}\n * @param b - second {@link RouteRecord}\n */\nfunction isSameRouteRecord(a, b) {\n    // since the original record has an undefined value for aliasOf\n    // but all aliases point to the original record, this will always compare\n    // the original record\n    return (a.aliasOf || a) === (b.aliasOf || b);\n}\nfunction isSameRouteLocationParams(a, b) {\n    if (Object.keys(a).length !== Object.keys(b).length)\n        return false;\n    for (const key in a) {\n        if (!isSameRouteLocationParamsValue(a[key], b[key]))\n            return false;\n    }\n    return true;\n}\nfunction isSameRouteLocationParamsValue(a, b) {\n    return isArray(a)\n        ? isEquivalentArray(a, b)\n        : isArray(b)\n            ? isEquivalentArray(b, a)\n            : a === b;\n}\n/**\n * Check if two arrays are the same or if an array with one single entry is the\n * same as another primitive value. Used to check query and parameters\n *\n * @param a - array of values\n * @param b - array of values or a single value\n */\nfunction isEquivalentArray(a, b) {\n    return isArray(b)\n        ? a.length === b.length && a.every((value, i) => value === b[i])\n        : a.length === 1 && a[0] === b;\n}\n/**\n * Resolves a relative path that starts with `.`.\n *\n * @param to - path location we are resolving\n * @param from - currentLocation.path, should start with `/`\n */\nfunction resolveRelativePath(to, from) {\n    if (to.startsWith('/'))\n        return to;\n    if ((process.env.NODE_ENV !== 'production') && !from.startsWith('/')) {\n        warn(`Cannot resolve a relative location without an absolute path. Trying to resolve \"${to}\" from \"${from}\". It should look like \"/${from}\".`);\n        return to;\n    }\n    if (!to)\n        return from;\n    const fromSegments = from.split('/');\n    const toSegments = to.split('/');\n    const lastToSegment = toSegments[toSegments.length - 1];\n    // make . and ./ the same (../ === .., ../../ === ../..)\n    // this is the same behavior as new URL()\n    if (lastToSegment === '..' || lastToSegment === '.') {\n        toSegments.push('');\n    }\n    let position = fromSegments.length - 1;\n    let toPosition;\n    let segment;\n    for (toPosition = 0; toPosition < toSegments.length; toPosition++) {\n        segment = toSegments[toPosition];\n        // we stay on the same position\n        if (segment === '.')\n            continue;\n        // go up in the from array\n        if (segment === '..') {\n            // we can't go below zero, but we still need to increment toPosition\n            if (position > 1)\n                position--;\n            // continue\n        }\n        // we reached a non-relative path, we stop here\n        else\n            break;\n    }\n    return (fromSegments.slice(0, position).join('/') +\n        '/' +\n        toSegments.slice(toPosition).join('/'));\n}\n/**\n * Initial route location where the router is. Can be used in navigation guards\n * to differentiate the initial navigation.\n *\n * @example\n * ```js\n * import { START_LOCATION } from 'vue-router'\n *\n * router.beforeEach((to, from) => {\n *   if (from === START_LOCATION) {\n *     // initial navigation\n *   }\n * })\n * ```\n */\nconst START_LOCATION_NORMALIZED = {\n    path: '/',\n    // TODO: could we use a symbol in the future?\n    name: undefined,\n    params: {},\n    query: {},\n    hash: '',\n    fullPath: '/',\n    matched: [],\n    meta: {},\n    redirectedFrom: undefined,\n};\n\nvar NavigationType;\n(function (NavigationType) {\n    NavigationType[\"pop\"] = \"pop\";\n    NavigationType[\"push\"] = \"push\";\n})(NavigationType || (NavigationType = {}));\nvar NavigationDirection;\n(function (NavigationDirection) {\n    NavigationDirection[\"back\"] = \"back\";\n    NavigationDirection[\"forward\"] = \"forward\";\n    NavigationDirection[\"unknown\"] = \"\";\n})(NavigationDirection || (NavigationDirection = {}));\n/**\n * Starting location for Histories\n */\nconst START = '';\n// Generic utils\n/**\n * Normalizes a base by removing any trailing slash and reading the base tag if\n * present.\n *\n * @param base - base to normalize\n */\nfunction normalizeBase(base) {\n    if (!base) {\n        if (isBrowser) {\n            // respect <base> tag\n            const baseEl = document.querySelector('base');\n            base = (baseEl && baseEl.getAttribute('href')) || '/';\n            // strip full URL origin\n            base = base.replace(/^\\w+:\\/\\/[^\\/]+/, '');\n        }\n        else {\n            base = '/';\n        }\n    }\n    // ensure leading slash when it was removed by the regex above avoid leading\n    // slash with hash because the file could be read from the disk like file://\n    // and the leading slash would cause problems\n    if (base[0] !== '/' && base[0] !== '#')\n        base = '/' + base;\n    // remove the trailing slash so all other method can just do `base + fullPath`\n    // to build an href\n    return removeTrailingSlash(base);\n}\n// remove any character before the hash\nconst BEFORE_HASH_RE = /^[^#]+#/;\nfunction createHref(base, location) {\n    return base.replace(BEFORE_HASH_RE, '#') + location;\n}\n\nfunction getElementPosition(el, offset) {\n    const docRect = document.documentElement.getBoundingClientRect();\n    const elRect = el.getBoundingClientRect();\n    return {\n        behavior: offset.behavior,\n        left: elRect.left - docRect.left - (offset.left || 0),\n        top: elRect.top - docRect.top - (offset.top || 0),\n    };\n}\nconst computeScrollPosition = () => ({\n    left: window.scrollX,\n    top: window.scrollY,\n});\nfunction scrollToPosition(position) {\n    let scrollToOptions;\n    if ('el' in position) {\n        const positionEl = position.el;\n        const isIdSelector = typeof positionEl === 'string' && positionEl.startsWith('#');\n        /**\n         * `id`s can accept pretty much any characters, including CSS combinators\n         * like `>` or `~`. It's still possible to retrieve elements using\n         * `document.getElementById('~')` but it needs to be escaped when using\n         * `document.querySelector('#\\\\~')` for it to be valid. The only\n         * requirements for `id`s are them to be unique on the page and to not be\n         * empty (`id=\"\"`). Because of that, when passing an id selector, it should\n         * be properly escaped for it to work with `querySelector`. We could check\n         * for the id selector to be simple (no CSS combinators `+ >~`) but that\n         * would make things inconsistent since they are valid characters for an\n         * `id` but would need to be escaped when using `querySelector`, breaking\n         * their usage and ending up in no selector returned. Selectors need to be\n         * escaped:\n         *\n         * - `#1-thing` becomes `#\\31 -thing`\n         * - `#with~symbols` becomes `#with\\\\~symbols`\n         *\n         * - More information about  the topic can be found at\n         *   https://mathiasbynens.be/notes/html5-id-class.\n         * - Practical example: https://mathiasbynens.be/demo/html5-id\n         */\n        if ((process.env.NODE_ENV !== 'production') && typeof position.el === 'string') {\n            if (!isIdSelector || !document.getElementById(position.el.slice(1))) {\n                try {\n                    const foundEl = document.querySelector(position.el);\n                    if (isIdSelector && foundEl) {\n                        warn(`The selector \"${position.el}\" should be passed as \"el: document.querySelector('${position.el}')\" because it starts with \"#\".`);\n                        // return to avoid other warnings\n                        return;\n                    }\n                }\n                catch (err) {\n                    warn(`The selector \"${position.el}\" is invalid. If you are using an id selector, make sure to escape it. You can find more information about escaping characters in selectors at https://mathiasbynens.be/notes/css-escapes or use CSS.escape (https://developer.mozilla.org/en-US/docs/Web/API/CSS/escape).`);\n                    // return to avoid other warnings\n                    return;\n                }\n            }\n        }\n        const el = typeof positionEl === 'string'\n            ? isIdSelector\n                ? document.getElementById(positionEl.slice(1))\n                : document.querySelector(positionEl)\n            : positionEl;\n        if (!el) {\n            (process.env.NODE_ENV !== 'production') &&\n                warn(`Couldn't find element using selector \"${position.el}\" returned by scrollBehavior.`);\n            return;\n        }\n        scrollToOptions = getElementPosition(el, position);\n    }\n    else {\n        scrollToOptions = position;\n    }\n    if ('scrollBehavior' in document.documentElement.style)\n        window.scrollTo(scrollToOptions);\n    else {\n        window.scrollTo(scrollToOptions.left != null ? scrollToOptions.left : window.scrollX, scrollToOptions.top != null ? scrollToOptions.top : window.scrollY);\n    }\n}\nfunction getScrollKey(path, delta) {\n    const position = history.state ? history.state.position - delta : -1;\n    return position + path;\n}\nconst scrollPositions = new Map();\nfunction saveScrollPosition(key, scrollPosition) {\n    scrollPositions.set(key, scrollPosition);\n}\nfunction getSavedScrollPosition(key) {\n    const scroll = scrollPositions.get(key);\n    // consume it so it's not used again\n    scrollPositions.delete(key);\n    return scroll;\n}\n// TODO: RFC about how to save scroll position\n/**\n * ScrollBehavior instance used by the router to compute and restore the scroll\n * position when navigating.\n */\n// export interface ScrollHandler<ScrollPositionEntry extends HistoryStateValue, ScrollPosition extends ScrollPositionEntry> {\n//   // returns a scroll position that can be saved in history\n//   compute(): ScrollPositionEntry\n//   // can take an extended ScrollPositionEntry\n//   scroll(position: ScrollPosition): void\n// }\n// export const scrollHandler: ScrollHandler<ScrollPosition> = {\n//   compute: computeScroll,\n//   scroll: scrollToPosition,\n// }\n\nlet createBaseLocation = () => location.protocol + '//' + location.host;\n/**\n * Creates a normalized history location from a window.location object\n * @param base - The base path\n * @param location - The window.location object\n */\nfunction createCurrentLocation(base, location) {\n    const { pathname, search, hash } = location;\n    // allows hash bases like #, /#, #/, #!, #!/, /#!/, or even /folder#end\n    const hashPos = base.indexOf('#');\n    if (hashPos > -1) {\n        let slicePos = hash.includes(base.slice(hashPos))\n            ? base.slice(hashPos).length\n            : 1;\n        let pathFromHash = hash.slice(slicePos);\n        // prepend the starting slash to hash so the url starts with /#\n        if (pathFromHash[0] !== '/')\n            pathFromHash = '/' + pathFromHash;\n        return stripBase(pathFromHash, '');\n    }\n    const path = stripBase(pathname, base);\n    return path + search + hash;\n}\nfunction useHistoryListeners(base, historyState, currentLocation, replace) {\n    let listeners = [];\n    let teardowns = [];\n    // TODO: should it be a stack? a Dict. Check if the popstate listener\n    // can trigger twice\n    let pauseState = null;\n    const popStateHandler = ({ state, }) => {\n        const to = createCurrentLocation(base, location);\n        const from = currentLocation.value;\n        const fromState = historyState.value;\n        let delta = 0;\n        if (state) {\n            currentLocation.value = to;\n            historyState.value = state;\n            // ignore the popstate and reset the pauseState\n            if (pauseState && pauseState === from) {\n                pauseState = null;\n                return;\n            }\n            delta = fromState ? state.position - fromState.position : 0;\n        }\n        else {\n            replace(to);\n        }\n        // Here we could also revert the navigation by calling history.go(-delta)\n        // this listener will have to be adapted to not trigger again and to wait for the url\n        // to be updated before triggering the listeners. Some kind of validation function would also\n        // need to be passed to the listeners so the navigation can be accepted\n        // call all listeners\n        listeners.forEach(listener => {\n            listener(currentLocation.value, from, {\n                delta,\n                type: NavigationType.pop,\n                direction: delta\n                    ? delta > 0\n                        ? NavigationDirection.forward\n                        : NavigationDirection.back\n                    : NavigationDirection.unknown,\n            });\n        });\n    };\n    function pauseListeners() {\n        pauseState = currentLocation.value;\n    }\n    function listen(callback) {\n        // set up the listener and prepare teardown callbacks\n        listeners.push(callback);\n        const teardown = () => {\n            const index = listeners.indexOf(callback);\n            if (index > -1)\n                listeners.splice(index, 1);\n        };\n        teardowns.push(teardown);\n        return teardown;\n    }\n    function beforeUnloadListener() {\n        const { history } = window;\n        if (!history.state)\n            return;\n        history.replaceState(assign({}, history.state, { scroll: computeScrollPosition() }), '');\n    }\n    function destroy() {\n        for (const teardown of teardowns)\n            teardown();\n        teardowns = [];\n        window.removeEventListener('popstate', popStateHandler);\n        window.removeEventListener('beforeunload', beforeUnloadListener);\n    }\n    // set up the listeners and prepare teardown callbacks\n    window.addEventListener('popstate', popStateHandler);\n    // TODO: could we use 'pagehide' or 'visibilitychange' instead?\n    // https://developer.chrome.com/blog/page-lifecycle-api/\n    window.addEventListener('beforeunload', beforeUnloadListener, {\n        passive: true,\n    });\n    return {\n        pauseListeners,\n        listen,\n        destroy,\n    };\n}\n/**\n * Creates a state object\n */\nfunction buildState(back, current, forward, replaced = false, computeScroll = false) {\n    return {\n        back,\n        current,\n        forward,\n        replaced,\n        position: window.history.length,\n        scroll: computeScroll ? computeScrollPosition() : null,\n    };\n}\nfunction useHistoryStateNavigation(base) {\n    const { history, location } = window;\n    // private variables\n    const currentLocation = {\n        value: createCurrentLocation(base, location),\n    };\n    const historyState = { value: history.state };\n    // build current history entry as this is a fresh navigation\n    if (!historyState.value) {\n        changeLocation(currentLocation.value, {\n            back: null,\n            current: currentLocation.value,\n            forward: null,\n            // the length is off by one, we need to decrease it\n            position: history.length - 1,\n            replaced: true,\n            // don't add a scroll as the user may have an anchor, and we want\n            // scrollBehavior to be triggered without a saved position\n            scroll: null,\n        }, true);\n    }\n    function changeLocation(to, state, replace) {\n        /**\n         * if a base tag is provided, and we are on a normal domain, we have to\n         * respect the provided `base` attribute because pushState() will use it and\n         * potentially erase anything before the `#` like at\n         * https://github.com/vuejs/router/issues/685 where a base of\n         * `/folder/#` but a base of `/` would erase the `/folder/` section. If\n         * there is no host, the `<base>` tag makes no sense and if there isn't a\n         * base tag we can just use everything after the `#`.\n         */\n        const hashIndex = base.indexOf('#');\n        const url = hashIndex > -1\n            ? (location.host && document.querySelector('base')\n                ? base\n                : base.slice(hashIndex)) + to\n            : createBaseLocation() + base + to;\n        try {\n            // BROWSER QUIRK\n            // NOTE: Safari throws a SecurityError when calling this function 100 times in 30 seconds\n            history[replace ? 'replaceState' : 'pushState'](state, '', url);\n            historyState.value = state;\n        }\n        catch (err) {\n            if ((process.env.NODE_ENV !== 'production')) {\n                warn('Error with push/replace State', err);\n            }\n            else {\n                console.error(err);\n            }\n            // Force the navigation, this also resets the call count\n            location[replace ? 'replace' : 'assign'](url);\n        }\n    }\n    function replace(to, data) {\n        const state = assign({}, history.state, buildState(historyState.value.back, \n        // keep back and forward entries but override current position\n        to, historyState.value.forward, true), data, { position: historyState.value.position });\n        changeLocation(to, state, true);\n        currentLocation.value = to;\n    }\n    function push(to, data) {\n        // Add to current entry the information of where we are going\n        // as well as saving the current position\n        const currentState = assign({}, \n        // use current history state to gracefully handle a wrong call to\n        // history.replaceState\n        // https://github.com/vuejs/router/issues/366\n        historyState.value, history.state, {\n            forward: to,\n            scroll: computeScrollPosition(),\n        });\n        if ((process.env.NODE_ENV !== 'production') && !history.state) {\n            warn(`history.state seems to have been manually replaced without preserving the necessary values. Make sure to preserve existing history state if you are manually calling history.replaceState:\\n\\n` +\n                `history.replaceState(history.state, '', url)\\n\\n` +\n                `You can find more information at https://router.vuejs.org/guide/migration/#Usage-of-history-state`);\n        }\n        changeLocation(currentState.current, currentState, true);\n        const state = assign({}, buildState(currentLocation.value, to, null), { position: currentState.position + 1 }, data);\n        changeLocation(to, state, false);\n        currentLocation.value = to;\n    }\n    return {\n        location: currentLocation,\n        state: historyState,\n        push,\n        replace,\n    };\n}\n/**\n * Creates an HTML5 history. Most common history for single page applications.\n *\n * @param base -\n */\nfunction createWebHistory(base) {\n    base = normalizeBase(base);\n    const historyNavigation = useHistoryStateNavigation(base);\n    const historyListeners = useHistoryListeners(base, historyNavigation.state, historyNavigation.location, historyNavigation.replace);\n    function go(delta, triggerListeners = true) {\n        if (!triggerListeners)\n            historyListeners.pauseListeners();\n        history.go(delta);\n    }\n    const routerHistory = assign({\n        // it's overridden right after\n        location: '',\n        base,\n        go,\n        createHref: createHref.bind(null, base),\n    }, historyNavigation, historyListeners);\n    Object.defineProperty(routerHistory, 'location', {\n        enumerable: true,\n        get: () => historyNavigation.location.value,\n    });\n    Object.defineProperty(routerHistory, 'state', {\n        enumerable: true,\n        get: () => historyNavigation.state.value,\n    });\n    return routerHistory;\n}\n\n/**\n * Creates an in-memory based history. The main purpose of this history is to handle SSR. It starts in a special location that is nowhere.\n * It's up to the user to replace that location with the starter location by either calling `router.push` or `router.replace`.\n *\n * @param base - Base applied to all urls, defaults to '/'\n * @returns a history object that can be passed to the router constructor\n */\nfunction createMemoryHistory(base = '') {\n    let listeners = [];\n    let queue = [[START, {}]];\n    let position = 0;\n    base = normalizeBase(base);\n    function setLocation(location, state = {}) {\n        position++;\n        if (position !== queue.length) {\n            // we are in the middle, we remove everything from here in the queue\n            queue.splice(position);\n        }\n        queue.push([location, state]);\n    }\n    function triggerListeners(to, from, { direction, delta }) {\n        const info = {\n            direction,\n            delta,\n            type: NavigationType.pop,\n        };\n        for (const callback of listeners) {\n            callback(to, from, info);\n        }\n    }\n    const routerHistory = {\n        // rewritten by Object.defineProperty\n        location: START,\n        // rewritten by Object.defineProperty\n        state: {},\n        base,\n        createHref: createHref.bind(null, base),\n        replace(to, state) {\n            // remove current entry and decrement position\n            queue.splice(position--, 1);\n            setLocation(to, state);\n        },\n        push(to, state) {\n            setLocation(to, state);\n        },\n        listen(callback) {\n            listeners.push(callback);\n            return () => {\n                const index = listeners.indexOf(callback);\n                if (index > -1)\n                    listeners.splice(index, 1);\n            };\n        },\n        destroy() {\n            listeners = [];\n            queue = [[START, {}]];\n            position = 0;\n        },\n        go(delta, shouldTrigger = true) {\n            const from = this.location;\n            const direction = \n            // we are considering delta === 0 going forward, but in abstract mode\n            // using 0 for the delta doesn't make sense like it does in html5 where\n            // it reloads the page\n            delta < 0 ? NavigationDirection.back : NavigationDirection.forward;\n            position = Math.max(0, Math.min(position + delta, queue.length - 1));\n            if (shouldTrigger) {\n                triggerListeners(this.location, from, {\n                    direction,\n                    delta,\n                });\n            }\n        },\n    };\n    Object.defineProperty(routerHistory, 'location', {\n        enumerable: true,\n        get: () => queue[position][0],\n    });\n    Object.defineProperty(routerHistory, 'state', {\n        enumerable: true,\n        get: () => queue[position][1],\n    });\n    return routerHistory;\n}\n\n/**\n * Creates a hash history. Useful for web applications with no host (e.g. `file://`) or when configuring a server to\n * handle any URL is not possible.\n *\n * @param base - optional base to provide. Defaults to `location.pathname + location.search` If there is a `<base>` tag\n * in the `head`, its value will be ignored in favor of this parameter **but note it affects all the history.pushState()\n * calls**, meaning that if you use a `<base>` tag, it's `href` value **has to match this parameter** (ignoring anything\n * after the `#`).\n *\n * @example\n * ```js\n * // at https://example.com/folder\n * createWebHashHistory() // gives a url of `https://example.com/folder#`\n * createWebHashHistory('/folder/') // gives a url of `https://example.com/folder/#`\n * // if the `#` is provided in the base, it won't be added by `createWebHashHistory`\n * createWebHashHistory('/folder/#/app/') // gives a url of `https://example.com/folder/#/app/`\n * // you should avoid doing this because it changes the original url and breaks copying urls\n * createWebHashHistory('/other-folder/') // gives a url of `https://example.com/other-folder/#`\n *\n * // at file:///usr/etc/folder/index.html\n * // for locations with no `host`, the base is ignored\n * createWebHashHistory('/iAmIgnored') // gives a url of `file:///usr/etc/folder/index.html#`\n * ```\n */\nfunction createWebHashHistory(base) {\n    // Make sure this implementation is fine in terms of encoding, specially for IE11\n    // for `file://`, directly use the pathname and ignore the base\n    // location.pathname contains an initial `/` even at the root: `https://example.com`\n    base = location.host ? base || location.pathname + location.search : '';\n    // allow the user to provide a `#` in the middle: `/base/#/app`\n    if (!base.includes('#'))\n        base += '#';\n    if ((process.env.NODE_ENV !== 'production') && !base.endsWith('#/') && !base.endsWith('#')) {\n        warn(`A hash base must end with a \"#\":\\n\"${base}\" should be \"${base.replace(/#.*$/, '#')}\".`);\n    }\n    return createWebHistory(base);\n}\n\nfunction isRouteLocation(route) {\n    return typeof route === 'string' || (route && typeof route === 'object');\n}\nfunction isRouteName(name) {\n    return typeof name === 'string' || typeof name === 'symbol';\n}\n\nconst NavigationFailureSymbol = Symbol((process.env.NODE_ENV !== 'production') ? 'navigation failure' : '');\n/**\n * Enumeration with all possible types for navigation failures. Can be passed to\n * {@link isNavigationFailure} to check for specific failures.\n */\nvar NavigationFailureType;\n(function (NavigationFailureType) {\n    /**\n     * An aborted navigation is a navigation that failed because a navigation\n     * guard returned `false` or called `next(false)`\n     */\n    NavigationFailureType[NavigationFailureType[\"aborted\"] = 4] = \"aborted\";\n    /**\n     * A cancelled navigation is a navigation that failed because a more recent\n     * navigation finished started (not necessarily finished).\n     */\n    NavigationFailureType[NavigationFailureType[\"cancelled\"] = 8] = \"cancelled\";\n    /**\n     * A duplicated navigation is a navigation that failed because it was\n     * initiated while already being at the exact same location.\n     */\n    NavigationFailureType[NavigationFailureType[\"duplicated\"] = 16] = \"duplicated\";\n})(NavigationFailureType || (NavigationFailureType = {}));\n// DEV only debug messages\nconst ErrorTypeMessages = {\n    [1 /* ErrorTypes.MATCHER_NOT_FOUND */]({ location, currentLocation }) {\n        return `No match for\\n ${JSON.stringify(location)}${currentLocation\n            ? '\\nwhile being at\\n' + JSON.stringify(currentLocation)\n            : ''}`;\n    },\n    [2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */]({ from, to, }) {\n        return `Redirected from \"${from.fullPath}\" to \"${stringifyRoute(to)}\" via a navigation guard.`;\n    },\n    [4 /* ErrorTypes.NAVIGATION_ABORTED */]({ from, to }) {\n        return `Navigation aborted from \"${from.fullPath}\" to \"${to.fullPath}\" via a navigation guard.`;\n    },\n    [8 /* ErrorTypes.NAVIGATION_CANCELLED */]({ from, to }) {\n        return `Navigation cancelled from \"${from.fullPath}\" to \"${to.fullPath}\" with a new navigation.`;\n    },\n    [16 /* ErrorTypes.NAVIGATION_DUPLICATED */]({ from, to }) {\n        return `Avoided redundant navigation to current location: \"${from.fullPath}\".`;\n    },\n};\n/**\n * Creates a typed NavigationFailure object.\n * @internal\n * @param type - NavigationFailureType\n * @param params - { from, to }\n */\nfunction createRouterError(type, params) {\n    // keep full error messages in cjs versions\n    if ((process.env.NODE_ENV !== 'production') || !true) {\n        return assign(new Error(ErrorTypeMessages[type](params)), {\n            type,\n            [NavigationFailureSymbol]: true,\n        }, params);\n    }\n    else {\n        return assign(new Error(), {\n            type,\n            [NavigationFailureSymbol]: true,\n        }, params);\n    }\n}\nfunction isNavigationFailure(error, type) {\n    return (error instanceof Error &&\n        NavigationFailureSymbol in error &&\n        (type == null || !!(error.type & type)));\n}\nconst propertiesToLog = ['params', 'query', 'hash'];\nfunction stringifyRoute(to) {\n    if (typeof to === 'string')\n        return to;\n    if (to.path != null)\n        return to.path;\n    const location = {};\n    for (const key of propertiesToLog) {\n        if (key in to)\n            location[key] = to[key];\n    }\n    return JSON.stringify(location, null, 2);\n}\n\n// default pattern for a param: non-greedy everything but /\nconst BASE_PARAM_PATTERN = '[^/]+?';\nconst BASE_PATH_PARSER_OPTIONS = {\n    sensitive: false,\n    strict: false,\n    start: true,\n    end: true,\n};\n// Special Regex characters that must be escaped in static tokens\nconst REGEX_CHARS_RE = /[.+*?^${}()[\\]/\\\\]/g;\n/**\n * Creates a path parser from an array of Segments (a segment is an array of Tokens)\n *\n * @param segments - array of segments returned by tokenizePath\n * @param extraOptions - optional options for the regexp\n * @returns a PathParser\n */\nfunction tokensToParser(segments, extraOptions) {\n    const options = assign({}, BASE_PATH_PARSER_OPTIONS, extraOptions);\n    // the amount of scores is the same as the length of segments except for the root segment \"/\"\n    const score = [];\n    // the regexp as a string\n    let pattern = options.start ? '^' : '';\n    // extracted keys\n    const keys = [];\n    for (const segment of segments) {\n        // the root segment needs special treatment\n        const segmentScores = segment.length ? [] : [90 /* PathScore.Root */];\n        // allow trailing slash\n        if (options.strict && !segment.length)\n            pattern += '/';\n        for (let tokenIndex = 0; tokenIndex < segment.length; tokenIndex++) {\n            const token = segment[tokenIndex];\n            // resets the score if we are inside a sub-segment /:a-other-:b\n            let subSegmentScore = 40 /* PathScore.Segment */ +\n                (options.sensitive ? 0.25 /* PathScore.BonusCaseSensitive */ : 0);\n            if (token.type === 0 /* TokenType.Static */) {\n                // prepend the slash if we are starting a new segment\n                if (!tokenIndex)\n                    pattern += '/';\n                pattern += token.value.replace(REGEX_CHARS_RE, '\\\\$&');\n                subSegmentScore += 40 /* PathScore.Static */;\n            }\n            else if (token.type === 1 /* TokenType.Param */) {\n                const { value, repeatable, optional, regexp } = token;\n                keys.push({\n                    name: value,\n                    repeatable,\n                    optional,\n                });\n                const re = regexp ? regexp : BASE_PARAM_PATTERN;\n                // the user provided a custom regexp /:id(\\\\d+)\n                if (re !== BASE_PARAM_PATTERN) {\n                    subSegmentScore += 10 /* PathScore.BonusCustomRegExp */;\n                    // make sure the regexp is valid before using it\n                    try {\n                        new RegExp(`(${re})`);\n                    }\n                    catch (err) {\n                        throw new Error(`Invalid custom RegExp for param \"${value}\" (${re}): ` +\n                            err.message);\n                    }\n                }\n                // when we repeat we must take care of the repeating leading slash\n                let subPattern = repeatable ? `((?:${re})(?:/(?:${re}))*)` : `(${re})`;\n                // prepend the slash if we are starting a new segment\n                if (!tokenIndex)\n                    subPattern =\n                        // avoid an optional / if there are more segments e.g. /:p?-static\n                        // or /:p?-:p2\n                        optional && segment.length < 2\n                            ? `(?:/${subPattern})`\n                            : '/' + subPattern;\n                if (optional)\n                    subPattern += '?';\n                pattern += subPattern;\n                subSegmentScore += 20 /* PathScore.Dynamic */;\n                if (optional)\n                    subSegmentScore += -8 /* PathScore.BonusOptional */;\n                if (repeatable)\n                    subSegmentScore += -20 /* PathScore.BonusRepeatable */;\n                if (re === '.*')\n                    subSegmentScore += -50 /* PathScore.BonusWildcard */;\n            }\n            segmentScores.push(subSegmentScore);\n        }\n        // an empty array like /home/<USER>\n        // if (!segment.length) pattern += '/'\n        score.push(segmentScores);\n    }\n    // only apply the strict bonus to the last score\n    if (options.strict && options.end) {\n        const i = score.length - 1;\n        score[i][score[i].length - 1] += 0.7000000000000001 /* PathScore.BonusStrict */;\n    }\n    // TODO: dev only warn double trailing slash\n    if (!options.strict)\n        pattern += '/?';\n    if (options.end)\n        pattern += '$';\n    // allow paths like /dynamic to only match dynamic or dynamic/... but not dynamic_something_else\n    else if (options.strict && !pattern.endsWith('/'))\n        pattern += '(?:/|$)';\n    const re = new RegExp(pattern, options.sensitive ? '' : 'i');\n    function parse(path) {\n        const match = path.match(re);\n        const params = {};\n        if (!match)\n            return null;\n        for (let i = 1; i < match.length; i++) {\n            const value = match[i] || '';\n            const key = keys[i - 1];\n            params[key.name] = value && key.repeatable ? value.split('/') : value;\n        }\n        return params;\n    }\n    function stringify(params) {\n        let path = '';\n        // for optional parameters to allow to be empty\n        let avoidDuplicatedSlash = false;\n        for (const segment of segments) {\n            if (!avoidDuplicatedSlash || !path.endsWith('/'))\n                path += '/';\n            avoidDuplicatedSlash = false;\n            for (const token of segment) {\n                if (token.type === 0 /* TokenType.Static */) {\n                    path += token.value;\n                }\n                else if (token.type === 1 /* TokenType.Param */) {\n                    const { value, repeatable, optional } = token;\n                    const param = value in params ? params[value] : '';\n                    if (isArray(param) && !repeatable) {\n                        throw new Error(`Provided param \"${value}\" is an array but it is not repeatable (* or + modifiers)`);\n                    }\n                    const text = isArray(param)\n                        ? param.join('/')\n                        : param;\n                    if (!text) {\n                        if (optional) {\n                            // if we have more than one optional param like /:a?-static we don't need to care about the optional param\n                            if (segment.length < 2) {\n                                // remove the last slash as we could be at the end\n                                if (path.endsWith('/'))\n                                    path = path.slice(0, -1);\n                                // do not append a slash on the next iteration\n                                else\n                                    avoidDuplicatedSlash = true;\n                            }\n                        }\n                        else\n                            throw new Error(`Missing required param \"${value}\"`);\n                    }\n                    path += text;\n                }\n            }\n        }\n        // avoid empty path when we have multiple optional params\n        return path || '/';\n    }\n    return {\n        re,\n        score,\n        keys,\n        parse,\n        stringify,\n    };\n}\n/**\n * Compares an array of numbers as used in PathParser.score and returns a\n * number. This function can be used to `sort` an array\n *\n * @param a - first array of numbers\n * @param b - second array of numbers\n * @returns 0 if both are equal, < 0 if a should be sorted first, > 0 if b\n * should be sorted first\n */\nfunction compareScoreArray(a, b) {\n    let i = 0;\n    while (i < a.length && i < b.length) {\n        const diff = b[i] - a[i];\n        // only keep going if diff === 0\n        if (diff)\n            return diff;\n        i++;\n    }\n    // if the last subsegment was Static, the shorter segments should be sorted first\n    // otherwise sort the longest segment first\n    if (a.length < b.length) {\n        return a.length === 1 && a[0] === 40 /* PathScore.Static */ + 40 /* PathScore.Segment */\n            ? -1\n            : 1;\n    }\n    else if (a.length > b.length) {\n        return b.length === 1 && b[0] === 40 /* PathScore.Static */ + 40 /* PathScore.Segment */\n            ? 1\n            : -1;\n    }\n    return 0;\n}\n/**\n * Compare function that can be used with `sort` to sort an array of PathParser\n *\n * @param a - first PathParser\n * @param b - second PathParser\n * @returns 0 if both are equal, < 0 if a should be sorted first, > 0 if b\n */\nfunction comparePathParserScore(a, b) {\n    let i = 0;\n    const aScore = a.score;\n    const bScore = b.score;\n    while (i < aScore.length && i < bScore.length) {\n        const comp = compareScoreArray(aScore[i], bScore[i]);\n        // do not return if both are equal\n        if (comp)\n            return comp;\n        i++;\n    }\n    if (Math.abs(bScore.length - aScore.length) === 1) {\n        if (isLastScoreNegative(aScore))\n            return 1;\n        if (isLastScoreNegative(bScore))\n            return -1;\n    }\n    // if a and b share the same score entries but b has more, sort b first\n    return bScore.length - aScore.length;\n    // this is the ternary version\n    // return aScore.length < bScore.length\n    //   ? 1\n    //   : aScore.length > bScore.length\n    //   ? -1\n    //   : 0\n}\n/**\n * This allows detecting splats at the end of a path: /home/<USER>\n *\n * @param score - score to check\n * @returns true if the last entry is negative\n */\nfunction isLastScoreNegative(score) {\n    const last = score[score.length - 1];\n    return score.length > 0 && last[last.length - 1] < 0;\n}\n\nconst ROOT_TOKEN = {\n    type: 0 /* TokenType.Static */,\n    value: '',\n};\nconst VALID_PARAM_RE = /[a-zA-Z0-9_]/;\n// After some profiling, the cache seems to be unnecessary because tokenizePath\n// (the slowest part of adding a route) is very fast\n// const tokenCache = new Map<string, Token[][]>()\nfunction tokenizePath(path) {\n    if (!path)\n        return [[]];\n    if (path === '/')\n        return [[ROOT_TOKEN]];\n    if (!path.startsWith('/')) {\n        throw new Error((process.env.NODE_ENV !== 'production')\n            ? `Route paths should start with a \"/\": \"${path}\" should be \"/${path}\".`\n            : `Invalid path \"${path}\"`);\n    }\n    // if (tokenCache.has(path)) return tokenCache.get(path)!\n    function crash(message) {\n        throw new Error(`ERR (${state})/\"${buffer}\": ${message}`);\n    }\n    let state = 0 /* TokenizerState.Static */;\n    let previousState = state;\n    const tokens = [];\n    // the segment will always be valid because we get into the initial state\n    // with the leading /\n    let segment;\n    function finalizeSegment() {\n        if (segment)\n            tokens.push(segment);\n        segment = [];\n    }\n    // index on the path\n    let i = 0;\n    // char at index\n    let char;\n    // buffer of the value read\n    let buffer = '';\n    // custom regexp for a param\n    let customRe = '';\n    function consumeBuffer() {\n        if (!buffer)\n            return;\n        if (state === 0 /* TokenizerState.Static */) {\n            segment.push({\n                type: 0 /* TokenType.Static */,\n                value: buffer,\n            });\n        }\n        else if (state === 1 /* TokenizerState.Param */ ||\n            state === 2 /* TokenizerState.ParamRegExp */ ||\n            state === 3 /* TokenizerState.ParamRegExpEnd */) {\n            if (segment.length > 1 && (char === '*' || char === '+'))\n                crash(`A repeatable param (${buffer}) must be alone in its segment. eg: '/:ids+.`);\n            segment.push({\n                type: 1 /* TokenType.Param */,\n                value: buffer,\n                regexp: customRe,\n                repeatable: char === '*' || char === '+',\n                optional: char === '*' || char === '?',\n            });\n        }\n        else {\n            crash('Invalid state to consume buffer');\n        }\n        buffer = '';\n    }\n    function addCharToBuffer() {\n        buffer += char;\n    }\n    while (i < path.length) {\n        char = path[i++];\n        if (char === '\\\\' && state !== 2 /* TokenizerState.ParamRegExp */) {\n            previousState = state;\n            state = 4 /* TokenizerState.EscapeNext */;\n            continue;\n        }\n        switch (state) {\n            case 0 /* TokenizerState.Static */:\n                if (char === '/') {\n                    if (buffer) {\n                        consumeBuffer();\n                    }\n                    finalizeSegment();\n                }\n                else if (char === ':') {\n                    consumeBuffer();\n                    state = 1 /* TokenizerState.Param */;\n                }\n                else {\n                    addCharToBuffer();\n                }\n                break;\n            case 4 /* TokenizerState.EscapeNext */:\n                addCharToBuffer();\n                state = previousState;\n                break;\n            case 1 /* TokenizerState.Param */:\n                if (char === '(') {\n                    state = 2 /* TokenizerState.ParamRegExp */;\n                }\n                else if (VALID_PARAM_RE.test(char)) {\n                    addCharToBuffer();\n                }\n                else {\n                    consumeBuffer();\n                    state = 0 /* TokenizerState.Static */;\n                    // go back one character if we were not modifying\n                    if (char !== '*' && char !== '?' && char !== '+')\n                        i--;\n                }\n                break;\n            case 2 /* TokenizerState.ParamRegExp */:\n                // TODO: is it worth handling nested regexp? like :p(?:prefix_([^/]+)_suffix)\n                // it already works by escaping the closing )\n                // https://paths.esm.dev/?p=AAMeJbiAwQEcDKbAoAAkP60PG2R6QAvgNaA6AFACM2ABuQBB#\n                // is this really something people need since you can also write\n                // /prefix_:p()_suffix\n                if (char === ')') {\n                    // handle the escaped )\n                    if (customRe[customRe.length - 1] == '\\\\')\n                        customRe = customRe.slice(0, -1) + char;\n                    else\n                        state = 3 /* TokenizerState.ParamRegExpEnd */;\n                }\n                else {\n                    customRe += char;\n                }\n                break;\n            case 3 /* TokenizerState.ParamRegExpEnd */:\n                // same as finalizing a param\n                consumeBuffer();\n                state = 0 /* TokenizerState.Static */;\n                // go back one character if we were not modifying\n                if (char !== '*' && char !== '?' && char !== '+')\n                    i--;\n                customRe = '';\n                break;\n            default:\n                crash('Unknown state');\n                break;\n        }\n    }\n    if (state === 2 /* TokenizerState.ParamRegExp */)\n        crash(`Unfinished custom RegExp for param \"${buffer}\"`);\n    consumeBuffer();\n    finalizeSegment();\n    // tokenCache.set(path, tokens)\n    return tokens;\n}\n\nfunction createRouteRecordMatcher(record, parent, options) {\n    const parser = tokensToParser(tokenizePath(record.path), options);\n    // warn against params with the same name\n    if ((process.env.NODE_ENV !== 'production')) {\n        const existingKeys = new Set();\n        for (const key of parser.keys) {\n            if (existingKeys.has(key.name))\n                warn(`Found duplicated params with name \"${key.name}\" for path \"${record.path}\". Only the last one will be available on \"$route.params\".`);\n            existingKeys.add(key.name);\n        }\n    }\n    const matcher = assign(parser, {\n        record,\n        parent,\n        // these needs to be populated by the parent\n        children: [],\n        alias: [],\n    });\n    if (parent) {\n        // both are aliases or both are not aliases\n        // we don't want to mix them because the order is used when\n        // passing originalRecord in Matcher.addRoute\n        if (!matcher.record.aliasOf === !parent.record.aliasOf)\n            parent.children.push(matcher);\n    }\n    return matcher;\n}\n\n/**\n * Creates a Router Matcher.\n *\n * @internal\n * @param routes - array of initial routes\n * @param globalOptions - global route options\n */\nfunction createRouterMatcher(routes, globalOptions) {\n    // normalized ordered array of matchers\n    const matchers = [];\n    const matcherMap = new Map();\n    globalOptions = mergeOptions({ strict: false, end: true, sensitive: false }, globalOptions);\n    function getRecordMatcher(name) {\n        return matcherMap.get(name);\n    }\n    function addRoute(record, parent, originalRecord) {\n        // used later on to remove by name\n        const isRootAdd = !originalRecord;\n        const mainNormalizedRecord = normalizeRouteRecord(record);\n        if ((process.env.NODE_ENV !== 'production')) {\n            checkChildMissingNameWithEmptyPath(mainNormalizedRecord, parent);\n        }\n        // we might be the child of an alias\n        mainNormalizedRecord.aliasOf = originalRecord && originalRecord.record;\n        const options = mergeOptions(globalOptions, record);\n        // generate an array of records to correctly handle aliases\n        const normalizedRecords = [mainNormalizedRecord];\n        if ('alias' in record) {\n            const aliases = typeof record.alias === 'string' ? [record.alias] : record.alias;\n            for (const alias of aliases) {\n                normalizedRecords.push(\n                // we need to normalize again to ensure the `mods` property\n                // being non enumerable\n                normalizeRouteRecord(assign({}, mainNormalizedRecord, {\n                    // this allows us to hold a copy of the `components` option\n                    // so that async components cache is hold on the original record\n                    components: originalRecord\n                        ? originalRecord.record.components\n                        : mainNormalizedRecord.components,\n                    path: alias,\n                    // we might be the child of an alias\n                    aliasOf: originalRecord\n                        ? originalRecord.record\n                        : mainNormalizedRecord,\n                    // the aliases are always of the same kind as the original since they\n                    // are defined on the same record\n                })));\n            }\n        }\n        let matcher;\n        let originalMatcher;\n        for (const normalizedRecord of normalizedRecords) {\n            const { path } = normalizedRecord;\n            // Build up the path for nested routes if the child isn't an absolute\n            // route. Only add the / delimiter if the child path isn't empty and if the\n            // parent path doesn't have a trailing slash\n            if (parent && path[0] !== '/') {\n                const parentPath = parent.record.path;\n                const connectingSlash = parentPath[parentPath.length - 1] === '/' ? '' : '/';\n                normalizedRecord.path =\n                    parent.record.path + (path && connectingSlash + path);\n            }\n            if ((process.env.NODE_ENV !== 'production') && normalizedRecord.path === '*') {\n                throw new Error('Catch all routes (\"*\") must now be defined using a param with a custom regexp.\\n' +\n                    'See more at https://router.vuejs.org/guide/migration/#Removed-star-or-catch-all-routes.');\n            }\n            // create the object beforehand, so it can be passed to children\n            matcher = createRouteRecordMatcher(normalizedRecord, parent, options);\n            if ((process.env.NODE_ENV !== 'production') && parent && path[0] === '/')\n                checkMissingParamsInAbsolutePath(matcher, parent);\n            // if we are an alias we must tell the original record that we exist,\n            // so we can be removed\n            if (originalRecord) {\n                originalRecord.alias.push(matcher);\n                if ((process.env.NODE_ENV !== 'production')) {\n                    checkSameParams(originalRecord, matcher);\n                }\n            }\n            else {\n                // otherwise, the first record is the original and others are aliases\n                originalMatcher = originalMatcher || matcher;\n                if (originalMatcher !== matcher)\n                    originalMatcher.alias.push(matcher);\n                // remove the route if named and only for the top record (avoid in nested calls)\n                // this works because the original record is the first one\n                if (isRootAdd && record.name && !isAliasRecord(matcher)) {\n                    if ((process.env.NODE_ENV !== 'production')) {\n                        checkSameNameAsAncestor(record, parent);\n                    }\n                    removeRoute(record.name);\n                }\n            }\n            // Avoid adding a record that doesn't display anything. This allows passing through records without a component to\n            // not be reached and pass through the catch all route\n            if (isMatchable(matcher)) {\n                insertMatcher(matcher);\n            }\n            if (mainNormalizedRecord.children) {\n                const children = mainNormalizedRecord.children;\n                for (let i = 0; i < children.length; i++) {\n                    addRoute(children[i], matcher, originalRecord && originalRecord.children[i]);\n                }\n            }\n            // if there was no original record, then the first one was not an alias and all\n            // other aliases (if any) need to reference this record when adding children\n            originalRecord = originalRecord || matcher;\n            // TODO: add normalized records for more flexibility\n            // if (parent && isAliasRecord(originalRecord)) {\n            //   parent.children.push(originalRecord)\n            // }\n        }\n        return originalMatcher\n            ? () => {\n                // since other matchers are aliases, they should be removed by the original matcher\n                removeRoute(originalMatcher);\n            }\n            : noop;\n    }\n    function removeRoute(matcherRef) {\n        if (isRouteName(matcherRef)) {\n            const matcher = matcherMap.get(matcherRef);\n            if (matcher) {\n                matcherMap.delete(matcherRef);\n                matchers.splice(matchers.indexOf(matcher), 1);\n                matcher.children.forEach(removeRoute);\n                matcher.alias.forEach(removeRoute);\n            }\n        }\n        else {\n            const index = matchers.indexOf(matcherRef);\n            if (index > -1) {\n                matchers.splice(index, 1);\n                if (matcherRef.record.name)\n                    matcherMap.delete(matcherRef.record.name);\n                matcherRef.children.forEach(removeRoute);\n                matcherRef.alias.forEach(removeRoute);\n            }\n        }\n    }\n    function getRoutes() {\n        return matchers;\n    }\n    function insertMatcher(matcher) {\n        const index = findInsertionIndex(matcher, matchers);\n        matchers.splice(index, 0, matcher);\n        // only add the original record to the name map\n        if (matcher.record.name && !isAliasRecord(matcher))\n            matcherMap.set(matcher.record.name, matcher);\n    }\n    function resolve(location, currentLocation) {\n        let matcher;\n        let params = {};\n        let path;\n        let name;\n        if ('name' in location && location.name) {\n            matcher = matcherMap.get(location.name);\n            if (!matcher)\n                throw createRouterError(1 /* ErrorTypes.MATCHER_NOT_FOUND */, {\n                    location,\n                });\n            // warn if the user is passing invalid params so they can debug it better when they get removed\n            if ((process.env.NODE_ENV !== 'production')) {\n                const invalidParams = Object.keys(location.params || {}).filter(paramName => !matcher.keys.find(k => k.name === paramName));\n                if (invalidParams.length) {\n                    warn(`Discarded invalid param(s) \"${invalidParams.join('\", \"')}\" when navigating. See https://github.com/vuejs/router/blob/main/packages/router/CHANGELOG.md#414-2022-08-22 for more details.`);\n                }\n            }\n            name = matcher.record.name;\n            params = assign(\n            // paramsFromLocation is a new object\n            paramsFromLocation(currentLocation.params, \n            // only keep params that exist in the resolved location\n            // only keep optional params coming from a parent record\n            matcher.keys\n                .filter(k => !k.optional)\n                .concat(matcher.parent ? matcher.parent.keys.filter(k => k.optional) : [])\n                .map(k => k.name)), \n            // discard any existing params in the current location that do not exist here\n            // #1497 this ensures better active/exact matching\n            location.params &&\n                paramsFromLocation(location.params, matcher.keys.map(k => k.name)));\n            // throws if cannot be stringified\n            path = matcher.stringify(params);\n        }\n        else if (location.path != null) {\n            // no need to resolve the path with the matcher as it was provided\n            // this also allows the user to control the encoding\n            path = location.path;\n            if ((process.env.NODE_ENV !== 'production') && !path.startsWith('/')) {\n                warn(`The Matcher cannot resolve relative paths but received \"${path}\". Unless you directly called \\`matcher.resolve(\"${path}\")\\`, this is probably a bug in vue-router. Please open an issue at https://github.com/vuejs/router/issues/new/choose.`);\n            }\n            matcher = matchers.find(m => m.re.test(path));\n            // matcher should have a value after the loop\n            if (matcher) {\n                // we know the matcher works because we tested the regexp\n                params = matcher.parse(path);\n                name = matcher.record.name;\n            }\n            // location is a relative path\n        }\n        else {\n            // match by name or path of current route\n            matcher = currentLocation.name\n                ? matcherMap.get(currentLocation.name)\n                : matchers.find(m => m.re.test(currentLocation.path));\n            if (!matcher)\n                throw createRouterError(1 /* ErrorTypes.MATCHER_NOT_FOUND */, {\n                    location,\n                    currentLocation,\n                });\n            name = matcher.record.name;\n            // since we are navigating to the same location, we don't need to pick the\n            // params like when `name` is provided\n            params = assign({}, currentLocation.params, location.params);\n            path = matcher.stringify(params);\n        }\n        const matched = [];\n        let parentMatcher = matcher;\n        while (parentMatcher) {\n            // reversed order so parents are at the beginning\n            matched.unshift(parentMatcher.record);\n            parentMatcher = parentMatcher.parent;\n        }\n        return {\n            name,\n            path,\n            params,\n            matched,\n            meta: mergeMetaFields(matched),\n        };\n    }\n    // add initial routes\n    routes.forEach(route => addRoute(route));\n    function clearRoutes() {\n        matchers.length = 0;\n        matcherMap.clear();\n    }\n    return {\n        addRoute,\n        resolve,\n        removeRoute,\n        clearRoutes,\n        getRoutes,\n        getRecordMatcher,\n    };\n}\nfunction paramsFromLocation(params, keys) {\n    const newParams = {};\n    for (const key of keys) {\n        if (key in params)\n            newParams[key] = params[key];\n    }\n    return newParams;\n}\n/**\n * Normalizes a RouteRecordRaw. Creates a copy\n *\n * @param record\n * @returns the normalized version\n */\nfunction normalizeRouteRecord(record) {\n    const normalized = {\n        path: record.path,\n        redirect: record.redirect,\n        name: record.name,\n        meta: record.meta || {},\n        aliasOf: record.aliasOf,\n        beforeEnter: record.beforeEnter,\n        props: normalizeRecordProps(record),\n        children: record.children || [],\n        instances: {},\n        leaveGuards: new Set(),\n        updateGuards: new Set(),\n        enterCallbacks: {},\n        // must be declared afterwards\n        // mods: {},\n        components: 'components' in record\n            ? record.components || null\n            : record.component && { default: record.component },\n    };\n    // mods contain modules and shouldn't be copied,\n    // logged or anything. It's just used for internal\n    // advanced use cases like data loaders\n    Object.defineProperty(normalized, 'mods', {\n        value: {},\n    });\n    return normalized;\n}\n/**\n * Normalize the optional `props` in a record to always be an object similar to\n * components. Also accept a boolean for components.\n * @param record\n */\nfunction normalizeRecordProps(record) {\n    const propsObject = {};\n    // props does not exist on redirect records, but we can set false directly\n    const props = record.props || false;\n    if ('component' in record) {\n        propsObject.default = props;\n    }\n    else {\n        // NOTE: we could also allow a function to be applied to every component.\n        // Would need user feedback for use cases\n        for (const name in record.components)\n            propsObject[name] = typeof props === 'object' ? props[name] : props;\n    }\n    return propsObject;\n}\n/**\n * Checks if a record or any of its parent is an alias\n * @param record\n */\nfunction isAliasRecord(record) {\n    while (record) {\n        if (record.record.aliasOf)\n            return true;\n        record = record.parent;\n    }\n    return false;\n}\n/**\n * Merge meta fields of an array of records\n *\n * @param matched - array of matched records\n */\nfunction mergeMetaFields(matched) {\n    return matched.reduce((meta, record) => assign(meta, record.meta), {});\n}\nfunction mergeOptions(defaults, partialOptions) {\n    const options = {};\n    for (const key in defaults) {\n        options[key] = key in partialOptions ? partialOptions[key] : defaults[key];\n    }\n    return options;\n}\nfunction isSameParam(a, b) {\n    return (a.name === b.name &&\n        a.optional === b.optional &&\n        a.repeatable === b.repeatable);\n}\n/**\n * Check if a path and its alias have the same required params\n *\n * @param a - original record\n * @param b - alias record\n */\nfunction checkSameParams(a, b) {\n    for (const key of a.keys) {\n        if (!key.optional && !b.keys.find(isSameParam.bind(null, key)))\n            return warn(`Alias \"${b.record.path}\" and the original record: \"${a.record.path}\" must have the exact same param named \"${key.name}\"`);\n    }\n    for (const key of b.keys) {\n        if (!key.optional && !a.keys.find(isSameParam.bind(null, key)))\n            return warn(`Alias \"${b.record.path}\" and the original record: \"${a.record.path}\" must have the exact same param named \"${key.name}\"`);\n    }\n}\n/**\n * A route with a name and a child with an empty path without a name should warn when adding the route\n *\n * @param mainNormalizedRecord - RouteRecordNormalized\n * @param parent - RouteRecordMatcher\n */\nfunction checkChildMissingNameWithEmptyPath(mainNormalizedRecord, parent) {\n    if (parent &&\n        parent.record.name &&\n        !mainNormalizedRecord.name &&\n        !mainNormalizedRecord.path) {\n        warn(`The route named \"${String(parent.record.name)}\" has a child without a name and an empty path. Using that name won't render the empty path child so you probably want to move the name to the child instead. If this is intentional, add a name to the child route to remove the warning.`);\n    }\n}\nfunction checkSameNameAsAncestor(record, parent) {\n    for (let ancestor = parent; ancestor; ancestor = ancestor.parent) {\n        if (ancestor.record.name === record.name) {\n            throw new Error(`A route named \"${String(record.name)}\" has been added as a ${parent === ancestor ? 'child' : 'descendant'} of a route with the same name. Route names must be unique and a nested route cannot use the same name as an ancestor.`);\n        }\n    }\n}\nfunction checkMissingParamsInAbsolutePath(record, parent) {\n    for (const key of parent.keys) {\n        if (!record.keys.find(isSameParam.bind(null, key)))\n            return warn(`Absolute path \"${record.record.path}\" must have the exact same param named \"${key.name}\" as its parent \"${parent.record.path}\".`);\n    }\n}\n/**\n * Performs a binary search to find the correct insertion index for a new matcher.\n *\n * Matchers are primarily sorted by their score. If scores are tied then we also consider parent/child relationships,\n * with descendants coming before ancestors. If there's still a tie, new routes are inserted after existing routes.\n *\n * @param matcher - new matcher to be inserted\n * @param matchers - existing matchers\n */\nfunction findInsertionIndex(matcher, matchers) {\n    // First phase: binary search based on score\n    let lower = 0;\n    let upper = matchers.length;\n    while (lower !== upper) {\n        const mid = (lower + upper) >> 1;\n        const sortOrder = comparePathParserScore(matcher, matchers[mid]);\n        if (sortOrder < 0) {\n            upper = mid;\n        }\n        else {\n            lower = mid + 1;\n        }\n    }\n    // Second phase: check for an ancestor with the same score\n    const insertionAncestor = getInsertionAncestor(matcher);\n    if (insertionAncestor) {\n        upper = matchers.lastIndexOf(insertionAncestor, upper - 1);\n        if ((process.env.NODE_ENV !== 'production') && upper < 0) {\n            // This should never happen\n            warn(`Finding ancestor route \"${insertionAncestor.record.path}\" failed for \"${matcher.record.path}\"`);\n        }\n    }\n    return upper;\n}\nfunction getInsertionAncestor(matcher) {\n    let ancestor = matcher;\n    while ((ancestor = ancestor.parent)) {\n        if (isMatchable(ancestor) &&\n            comparePathParserScore(matcher, ancestor) === 0) {\n            return ancestor;\n        }\n    }\n    return;\n}\n/**\n * Checks if a matcher can be reachable. This means if it's possible to reach it as a route. For example, routes without\n * a component, or name, or redirect, are just used to group other routes.\n * @param matcher\n * @param matcher.record record of the matcher\n * @returns\n */\nfunction isMatchable({ record }) {\n    return !!(record.name ||\n        (record.components && Object.keys(record.components).length) ||\n        record.redirect);\n}\n\n/**\n * Transforms a queryString into a {@link LocationQuery} object. Accept both, a\n * version with the leading `?` and without Should work as URLSearchParams\n\n * @internal\n *\n * @param search - search string to parse\n * @returns a query object\n */\nfunction parseQuery(search) {\n    const query = {};\n    // avoid creating an object with an empty key and empty value\n    // because of split('&')\n    if (search === '' || search === '?')\n        return query;\n    const hasLeadingIM = search[0] === '?';\n    const searchParams = (hasLeadingIM ? search.slice(1) : search).split('&');\n    for (let i = 0; i < searchParams.length; ++i) {\n        // pre decode the + into space\n        const searchParam = searchParams[i].replace(PLUS_RE, ' ');\n        // allow the = character\n        const eqPos = searchParam.indexOf('=');\n        const key = decode(eqPos < 0 ? searchParam : searchParam.slice(0, eqPos));\n        const value = eqPos < 0 ? null : decode(searchParam.slice(eqPos + 1));\n        if (key in query) {\n            // an extra variable for ts types\n            let currentValue = query[key];\n            if (!isArray(currentValue)) {\n                currentValue = query[key] = [currentValue];\n            }\n            currentValue.push(value);\n        }\n        else {\n            query[key] = value;\n        }\n    }\n    return query;\n}\n/**\n * Stringifies a {@link LocationQueryRaw} object. Like `URLSearchParams`, it\n * doesn't prepend a `?`\n *\n * @internal\n *\n * @param query - query object to stringify\n * @returns string version of the query without the leading `?`\n */\nfunction stringifyQuery(query) {\n    let search = '';\n    for (let key in query) {\n        const value = query[key];\n        key = encodeQueryKey(key);\n        if (value == null) {\n            // only null adds the value\n            if (value !== undefined) {\n                search += (search.length ? '&' : '') + key;\n            }\n            continue;\n        }\n        // keep null values\n        const values = isArray(value)\n            ? value.map(v => v && encodeQueryValue(v))\n            : [value && encodeQueryValue(value)];\n        values.forEach(value => {\n            // skip undefined values in arrays as if they were not present\n            // smaller code than using filter\n            if (value !== undefined) {\n                // only append & with non-empty search\n                search += (search.length ? '&' : '') + key;\n                if (value != null)\n                    search += '=' + value;\n            }\n        });\n    }\n    return search;\n}\n/**\n * Transforms a {@link LocationQueryRaw} into a {@link LocationQuery} by casting\n * numbers into strings, removing keys with an undefined value and replacing\n * undefined with null in arrays\n *\n * @param query - query object to normalize\n * @returns a normalized query object\n */\nfunction normalizeQuery(query) {\n    const normalizedQuery = {};\n    for (const key in query) {\n        const value = query[key];\n        if (value !== undefined) {\n            normalizedQuery[key] = isArray(value)\n                ? value.map(v => (v == null ? null : '' + v))\n                : value == null\n                    ? value\n                    : '' + value;\n        }\n    }\n    return normalizedQuery;\n}\n\n/**\n * RouteRecord being rendered by the closest ancestor Router View. Used for\n * `onBeforeRouteUpdate` and `onBeforeRouteLeave`. rvlm stands for Router View\n * Location Matched\n *\n * @internal\n */\nconst matchedRouteKey = Symbol((process.env.NODE_ENV !== 'production') ? 'router view location matched' : '');\n/**\n * Allows overriding the router view depth to control which component in\n * `matched` is rendered. rvd stands for Router View Depth\n *\n * @internal\n */\nconst viewDepthKey = Symbol((process.env.NODE_ENV !== 'production') ? 'router view depth' : '');\n/**\n * Allows overriding the router instance returned by `useRouter` in tests. r\n * stands for router\n *\n * @internal\n */\nconst routerKey = Symbol((process.env.NODE_ENV !== 'production') ? 'router' : '');\n/**\n * Allows overriding the current route returned by `useRoute` in tests. rl\n * stands for route location\n *\n * @internal\n */\nconst routeLocationKey = Symbol((process.env.NODE_ENV !== 'production') ? 'route location' : '');\n/**\n * Allows overriding the current route used by router-view. Internally this is\n * used when the `route` prop is passed.\n *\n * @internal\n */\nconst routerViewLocationKey = Symbol((process.env.NODE_ENV !== 'production') ? 'router view location' : '');\n\n/**\n * Create a list of callbacks that can be reset. Used to create before and after navigation guards list\n */\nfunction useCallbacks() {\n    let handlers = [];\n    function add(handler) {\n        handlers.push(handler);\n        return () => {\n            const i = handlers.indexOf(handler);\n            if (i > -1)\n                handlers.splice(i, 1);\n        };\n    }\n    function reset() {\n        handlers = [];\n    }\n    return {\n        add,\n        list: () => handlers.slice(),\n        reset,\n    };\n}\n\nfunction registerGuard(record, name, guard) {\n    const removeFromList = () => {\n        record[name].delete(guard);\n    };\n    onUnmounted(removeFromList);\n    onDeactivated(removeFromList);\n    onActivated(() => {\n        record[name].add(guard);\n    });\n    record[name].add(guard);\n}\n/**\n * Add a navigation guard that triggers whenever the component for the current\n * location is about to be left. Similar to {@link beforeRouteLeave} but can be\n * used in any component. The guard is removed when the component is unmounted.\n *\n * @param leaveGuard - {@link NavigationGuard}\n */\nfunction onBeforeRouteLeave(leaveGuard) {\n    if ((process.env.NODE_ENV !== 'production') && !getCurrentInstance()) {\n        warn('getCurrentInstance() returned null. onBeforeRouteLeave() must be called at the top of a setup function');\n        return;\n    }\n    const activeRecord = inject(matchedRouteKey, \n    // to avoid warning\n    {}).value;\n    if (!activeRecord) {\n        (process.env.NODE_ENV !== 'production') &&\n            warn('No active route record was found when calling `onBeforeRouteLeave()`. Make sure you call this function inside a component child of <router-view>. Maybe you called it inside of App.vue?');\n        return;\n    }\n    registerGuard(activeRecord, 'leaveGuards', leaveGuard);\n}\n/**\n * Add a navigation guard that triggers whenever the current location is about\n * to be updated. Similar to {@link beforeRouteUpdate} but can be used in any\n * component. The guard is removed when the component is unmounted.\n *\n * @param updateGuard - {@link NavigationGuard}\n */\nfunction onBeforeRouteUpdate(updateGuard) {\n    if ((process.env.NODE_ENV !== 'production') && !getCurrentInstance()) {\n        warn('getCurrentInstance() returned null. onBeforeRouteUpdate() must be called at the top of a setup function');\n        return;\n    }\n    const activeRecord = inject(matchedRouteKey, \n    // to avoid warning\n    {}).value;\n    if (!activeRecord) {\n        (process.env.NODE_ENV !== 'production') &&\n            warn('No active route record was found when calling `onBeforeRouteUpdate()`. Make sure you call this function inside a component child of <router-view>. Maybe you called it inside of App.vue?');\n        return;\n    }\n    registerGuard(activeRecord, 'updateGuards', updateGuard);\n}\nfunction guardToPromiseFn(guard, to, from, record, name, runWithContext = fn => fn()) {\n    // keep a reference to the enterCallbackArray to prevent pushing callbacks if a new navigation took place\n    const enterCallbackArray = record &&\n        // name is defined if record is because of the function overload\n        (record.enterCallbacks[name] = record.enterCallbacks[name] || []);\n    return () => new Promise((resolve, reject) => {\n        const next = (valid) => {\n            if (valid === false) {\n                reject(createRouterError(4 /* ErrorTypes.NAVIGATION_ABORTED */, {\n                    from,\n                    to,\n                }));\n            }\n            else if (valid instanceof Error) {\n                reject(valid);\n            }\n            else if (isRouteLocation(valid)) {\n                reject(createRouterError(2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */, {\n                    from: to,\n                    to: valid,\n                }));\n            }\n            else {\n                if (enterCallbackArray &&\n                    // since enterCallbackArray is truthy, both record and name also are\n                    record.enterCallbacks[name] === enterCallbackArray &&\n                    typeof valid === 'function') {\n                    enterCallbackArray.push(valid);\n                }\n                resolve();\n            }\n        };\n        // wrapping with Promise.resolve allows it to work with both async and sync guards\n        const guardReturn = runWithContext(() => guard.call(record && record.instances[name], to, from, (process.env.NODE_ENV !== 'production') ? canOnlyBeCalledOnce(next, to, from) : next));\n        let guardCall = Promise.resolve(guardReturn);\n        if (guard.length < 3)\n            guardCall = guardCall.then(next);\n        if ((process.env.NODE_ENV !== 'production') && guard.length > 2) {\n            const message = `The \"next\" callback was never called inside of ${guard.name ? '\"' + guard.name + '\"' : ''}:\\n${guard.toString()}\\n. If you are returning a value instead of calling \"next\", make sure to remove the \"next\" parameter from your function.`;\n            if (typeof guardReturn === 'object' && 'then' in guardReturn) {\n                guardCall = guardCall.then(resolvedValue => {\n                    // @ts-expect-error: _called is added at canOnlyBeCalledOnce\n                    if (!next._called) {\n                        warn(message);\n                        return Promise.reject(new Error('Invalid navigation guard'));\n                    }\n                    return resolvedValue;\n                });\n            }\n            else if (guardReturn !== undefined) {\n                // @ts-expect-error: _called is added at canOnlyBeCalledOnce\n                if (!next._called) {\n                    warn(message);\n                    reject(new Error('Invalid navigation guard'));\n                    return;\n                }\n            }\n        }\n        guardCall.catch(err => reject(err));\n    });\n}\nfunction canOnlyBeCalledOnce(next, to, from) {\n    let called = 0;\n    return function () {\n        if (called++ === 1)\n            warn(`The \"next\" callback was called more than once in one navigation guard when going from \"${from.fullPath}\" to \"${to.fullPath}\". It should be called exactly one time in each navigation guard. This will fail in production.`);\n        // @ts-expect-error: we put it in the original one because it's easier to check\n        next._called = true;\n        if (called === 1)\n            next.apply(null, arguments);\n    };\n}\nfunction extractComponentsGuards(matched, guardType, to, from, runWithContext = fn => fn()) {\n    const guards = [];\n    for (const record of matched) {\n        if ((process.env.NODE_ENV !== 'production') && !record.components && !record.children.length) {\n            warn(`Record with path \"${record.path}\" is either missing a \"component(s)\"` +\n                ` or \"children\" property.`);\n        }\n        for (const name in record.components) {\n            let rawComponent = record.components[name];\n            if ((process.env.NODE_ENV !== 'production')) {\n                if (!rawComponent ||\n                    (typeof rawComponent !== 'object' &&\n                        typeof rawComponent !== 'function')) {\n                    warn(`Component \"${name}\" in record with path \"${record.path}\" is not` +\n                        ` a valid component. Received \"${String(rawComponent)}\".`);\n                    // throw to ensure we stop here but warn to ensure the message isn't\n                    // missed by the user\n                    throw new Error('Invalid route component');\n                }\n                else if ('then' in rawComponent) {\n                    // warn if user wrote import('/component.vue') instead of () =>\n                    // import('./component.vue')\n                    warn(`Component \"${name}\" in record with path \"${record.path}\" is a ` +\n                        `Promise instead of a function that returns a Promise. Did you ` +\n                        `write \"import('./MyPage.vue')\" instead of ` +\n                        `\"() => import('./MyPage.vue')\" ? This will break in ` +\n                        `production if not fixed.`);\n                    const promise = rawComponent;\n                    rawComponent = () => promise;\n                }\n                else if (rawComponent.__asyncLoader &&\n                    // warn only once per component\n                    !rawComponent.__warnedDefineAsync) {\n                    rawComponent.__warnedDefineAsync = true;\n                    warn(`Component \"${name}\" in record with path \"${record.path}\" is defined ` +\n                        `using \"defineAsyncComponent()\". ` +\n                        `Write \"() => import('./MyPage.vue')\" instead of ` +\n                        `\"defineAsyncComponent(() => import('./MyPage.vue'))\".`);\n                }\n            }\n            // skip update and leave guards if the route component is not mounted\n            if (guardType !== 'beforeRouteEnter' && !record.instances[name])\n                continue;\n            if (isRouteComponent(rawComponent)) {\n                // __vccOpts is added by vue-class-component and contain the regular options\n                const options = rawComponent.__vccOpts || rawComponent;\n                const guard = options[guardType];\n                guard &&\n                    guards.push(guardToPromiseFn(guard, to, from, record, name, runWithContext));\n            }\n            else {\n                // start requesting the chunk already\n                let componentPromise = rawComponent();\n                if ((process.env.NODE_ENV !== 'production') && !('catch' in componentPromise)) {\n                    warn(`Component \"${name}\" in record with path \"${record.path}\" is a function that does not return a Promise. If you were passing a functional component, make sure to add a \"displayName\" to the component. This will break in production if not fixed.`);\n                    componentPromise = Promise.resolve(componentPromise);\n                }\n                guards.push(() => componentPromise.then(resolved => {\n                    if (!resolved)\n                        throw new Error(`Couldn't resolve component \"${name}\" at \"${record.path}\"`);\n                    const resolvedComponent = isESModule(resolved)\n                        ? resolved.default\n                        : resolved;\n                    // keep the resolved module for plugins like data loaders\n                    record.mods[name] = resolved;\n                    // replace the function with the resolved component\n                    // cannot be null or undefined because we went into the for loop\n                    record.components[name] = resolvedComponent;\n                    // __vccOpts is added by vue-class-component and contain the regular options\n                    const options = resolvedComponent.__vccOpts || resolvedComponent;\n                    const guard = options[guardType];\n                    return (guard &&\n                        guardToPromiseFn(guard, to, from, record, name, runWithContext)());\n                }));\n            }\n        }\n    }\n    return guards;\n}\n/**\n * Ensures a route is loaded, so it can be passed as o prop to `<RouterView>`.\n *\n * @param route - resolved route to load\n */\nfunction loadRouteLocation(route) {\n    return route.matched.every(record => record.redirect)\n        ? Promise.reject(new Error('Cannot load a route that redirects.'))\n        : Promise.all(route.matched.map(record => record.components &&\n            Promise.all(Object.keys(record.components).reduce((promises, name) => {\n                const rawComponent = record.components[name];\n                if (typeof rawComponent === 'function' &&\n                    !('displayName' in rawComponent)) {\n                    promises.push(rawComponent().then(resolved => {\n                        if (!resolved)\n                            return Promise.reject(new Error(`Couldn't resolve component \"${name}\" at \"${record.path}\". Ensure you passed a function that returns a promise.`));\n                        const resolvedComponent = isESModule(resolved)\n                            ? resolved.default\n                            : resolved;\n                        // keep the resolved module for plugins like data loaders\n                        record.mods[name] = resolved;\n                        // replace the function with the resolved component\n                        // cannot be null or undefined because we went into the for loop\n                        record.components[name] = resolvedComponent;\n                        return;\n                    }));\n                }\n                return promises;\n            }, [])))).then(() => route);\n}\n\n// TODO: we could allow currentRoute as a prop to expose `isActive` and\n// `isExactActive` behavior should go through an RFC\n/**\n * Returns the internal behavior of a {@link RouterLink} without the rendering part.\n *\n * @param props - a `to` location and an optional `replace` flag\n */\nfunction useLink(props) {\n    const router = inject(routerKey);\n    const currentRoute = inject(routeLocationKey);\n    let hasPrevious = false;\n    let previousTo = null;\n    const route = computed(() => {\n        const to = unref(props.to);\n        if ((process.env.NODE_ENV !== 'production') && (!hasPrevious || to !== previousTo)) {\n            if (!isRouteLocation(to)) {\n                if (hasPrevious) {\n                    warn(`Invalid value for prop \"to\" in useLink()\\n- to:`, to, `\\n- previous to:`, previousTo, `\\n- props:`, props);\n                }\n                else {\n                    warn(`Invalid value for prop \"to\" in useLink()\\n- to:`, to, `\\n- props:`, props);\n                }\n            }\n            previousTo = to;\n            hasPrevious = true;\n        }\n        return router.resolve(to);\n    });\n    const activeRecordIndex = computed(() => {\n        const { matched } = route.value;\n        const { length } = matched;\n        const routeMatched = matched[length - 1];\n        const currentMatched = currentRoute.matched;\n        if (!routeMatched || !currentMatched.length)\n            return -1;\n        const index = currentMatched.findIndex(isSameRouteRecord.bind(null, routeMatched));\n        if (index > -1)\n            return index;\n        // possible parent record\n        const parentRecordPath = getOriginalPath(matched[length - 2]);\n        return (\n        // we are dealing with nested routes\n        length > 1 &&\n            // if the parent and matched route have the same path, this link is\n            // referring to the empty child. Or we currently are on a different\n            // child of the same parent\n            getOriginalPath(routeMatched) === parentRecordPath &&\n            // avoid comparing the child with its parent\n            currentMatched[currentMatched.length - 1].path !== parentRecordPath\n            ? currentMatched.findIndex(isSameRouteRecord.bind(null, matched[length - 2]))\n            : index);\n    });\n    const isActive = computed(() => activeRecordIndex.value > -1 &&\n        includesParams(currentRoute.params, route.value.params));\n    const isExactActive = computed(() => activeRecordIndex.value > -1 &&\n        activeRecordIndex.value === currentRoute.matched.length - 1 &&\n        isSameRouteLocationParams(currentRoute.params, route.value.params));\n    function navigate(e = {}) {\n        if (guardEvent(e)) {\n            const p = router[unref(props.replace) ? 'replace' : 'push'](unref(props.to)\n            // avoid uncaught errors are they are logged anyway\n            ).catch(noop);\n            if (props.viewTransition &&\n                typeof document !== 'undefined' &&\n                'startViewTransition' in document) {\n                document.startViewTransition(() => p);\n            }\n            return p;\n        }\n        return Promise.resolve();\n    }\n    // devtools only\n    if (((process.env.NODE_ENV !== 'production') || __VUE_PROD_DEVTOOLS__) && isBrowser) {\n        const instance = getCurrentInstance();\n        if (instance) {\n            const linkContextDevtools = {\n                route: route.value,\n                isActive: isActive.value,\n                isExactActive: isExactActive.value,\n                error: null,\n            };\n            // @ts-expect-error: this is internal\n            instance.__vrl_devtools = instance.__vrl_devtools || [];\n            // @ts-expect-error: this is internal\n            instance.__vrl_devtools.push(linkContextDevtools);\n            watchEffect(() => {\n                linkContextDevtools.route = route.value;\n                linkContextDevtools.isActive = isActive.value;\n                linkContextDevtools.isExactActive = isExactActive.value;\n                linkContextDevtools.error = isRouteLocation(unref(props.to))\n                    ? null\n                    : 'Invalid \"to\" value';\n            }, { flush: 'post' });\n        }\n    }\n    /**\n     * NOTE: update {@link _RouterLinkI}'s `$slots` type when updating this\n     */\n    return {\n        route,\n        href: computed(() => route.value.href),\n        isActive,\n        isExactActive,\n        navigate,\n    };\n}\nfunction preferSingleVNode(vnodes) {\n    return vnodes.length === 1 ? vnodes[0] : vnodes;\n}\nconst RouterLinkImpl = /*#__PURE__*/ defineComponent({\n    name: 'RouterLink',\n    compatConfig: { MODE: 3 },\n    props: {\n        to: {\n            type: [String, Object],\n            required: true,\n        },\n        replace: Boolean,\n        activeClass: String,\n        // inactiveClass: String,\n        exactActiveClass: String,\n        custom: Boolean,\n        ariaCurrentValue: {\n            type: String,\n            default: 'page',\n        },\n        viewTransition: Boolean,\n    },\n    useLink,\n    setup(props, { slots }) {\n        const link = reactive(useLink(props));\n        const { options } = inject(routerKey);\n        const elClass = computed(() => ({\n            [getLinkClass(props.activeClass, options.linkActiveClass, 'router-link-active')]: link.isActive,\n            // [getLinkClass(\n            //   props.inactiveClass,\n            //   options.linkInactiveClass,\n            //   'router-link-inactive'\n            // )]: !link.isExactActive,\n            [getLinkClass(props.exactActiveClass, options.linkExactActiveClass, 'router-link-exact-active')]: link.isExactActive,\n        }));\n        return () => {\n            const children = slots.default && preferSingleVNode(slots.default(link));\n            return props.custom\n                ? children\n                : h('a', {\n                    'aria-current': link.isExactActive\n                        ? props.ariaCurrentValue\n                        : null,\n                    href: link.href,\n                    // this would override user added attrs but Vue will still add\n                    // the listener, so we end up triggering both\n                    onClick: link.navigate,\n                    class: elClass.value,\n                }, children);\n        };\n    },\n});\n// export the public type for h/tsx inference\n// also to avoid inline import() in generated d.ts files\n/**\n * Component to render a link that triggers a navigation on click.\n */\nconst RouterLink = RouterLinkImpl;\nfunction guardEvent(e) {\n    // don't redirect with control keys\n    if (e.metaKey || e.altKey || e.ctrlKey || e.shiftKey)\n        return;\n    // don't redirect when preventDefault called\n    if (e.defaultPrevented)\n        return;\n    // don't redirect on right click\n    if (e.button !== undefined && e.button !== 0)\n        return;\n    // don't redirect if `target=\"_blank\"`\n    // @ts-expect-error getAttribute does exist\n    if (e.currentTarget && e.currentTarget.getAttribute) {\n        // @ts-expect-error getAttribute exists\n        const target = e.currentTarget.getAttribute('target');\n        if (/\\b_blank\\b/i.test(target))\n            return;\n    }\n    // this may be a Weex event which doesn't have this method\n    if (e.preventDefault)\n        e.preventDefault();\n    return true;\n}\nfunction includesParams(outer, inner) {\n    for (const key in inner) {\n        const innerValue = inner[key];\n        const outerValue = outer[key];\n        if (typeof innerValue === 'string') {\n            if (innerValue !== outerValue)\n                return false;\n        }\n        else {\n            if (!isArray(outerValue) ||\n                outerValue.length !== innerValue.length ||\n                innerValue.some((value, i) => value !== outerValue[i]))\n                return false;\n        }\n    }\n    return true;\n}\n/**\n * Get the original path value of a record by following its aliasOf\n * @param record\n */\nfunction getOriginalPath(record) {\n    return record ? (record.aliasOf ? record.aliasOf.path : record.path) : '';\n}\n/**\n * Utility class to get the active class based on defaults.\n * @param propClass\n * @param globalClass\n * @param defaultClass\n */\nconst getLinkClass = (propClass, globalClass, defaultClass) => propClass != null\n    ? propClass\n    : globalClass != null\n        ? globalClass\n        : defaultClass;\n\nconst RouterViewImpl = /*#__PURE__*/ defineComponent({\n    name: 'RouterView',\n    // #674 we manually inherit them\n    inheritAttrs: false,\n    props: {\n        name: {\n            type: String,\n            default: 'default',\n        },\n        route: Object,\n    },\n    // Better compat for @vue/compat users\n    // https://github.com/vuejs/router/issues/1315\n    compatConfig: { MODE: 3 },\n    setup(props, { attrs, slots }) {\n        (process.env.NODE_ENV !== 'production') && warnDeprecatedUsage();\n        const injectedRoute = inject(routerViewLocationKey);\n        const routeToDisplay = computed(() => props.route || injectedRoute.value);\n        const injectedDepth = inject(viewDepthKey, 0);\n        // The depth changes based on empty components option, which allows passthrough routes e.g. routes with children\n        // that are used to reuse the `path` property\n        const depth = computed(() => {\n            let initialDepth = unref(injectedDepth);\n            const { matched } = routeToDisplay.value;\n            let matchedRoute;\n            while ((matchedRoute = matched[initialDepth]) &&\n                !matchedRoute.components) {\n                initialDepth++;\n            }\n            return initialDepth;\n        });\n        const matchedRouteRef = computed(() => routeToDisplay.value.matched[depth.value]);\n        provide(viewDepthKey, computed(() => depth.value + 1));\n        provide(matchedRouteKey, matchedRouteRef);\n        provide(routerViewLocationKey, routeToDisplay);\n        const viewRef = ref();\n        // watch at the same time the component instance, the route record we are\n        // rendering, and the name\n        watch(() => [viewRef.value, matchedRouteRef.value, props.name], ([instance, to, name], [oldInstance, from, oldName]) => {\n            // copy reused instances\n            if (to) {\n                // this will update the instance for new instances as well as reused\n                // instances when navigating to a new route\n                to.instances[name] = instance;\n                // the component instance is reused for a different route or name, so\n                // we copy any saved update or leave guards. With async setup, the\n                // mounting component will mount before the matchedRoute changes,\n                // making instance === oldInstance, so we check if guards have been\n                // added before. This works because we remove guards when\n                // unmounting/deactivating components\n                if (from && from !== to && instance && instance === oldInstance) {\n                    if (!to.leaveGuards.size) {\n                        to.leaveGuards = from.leaveGuards;\n                    }\n                    if (!to.updateGuards.size) {\n                        to.updateGuards = from.updateGuards;\n                    }\n                }\n            }\n            // trigger beforeRouteEnter next callbacks\n            if (instance &&\n                to &&\n                // if there is no instance but to and from are the same this might be\n                // the first visit\n                (!from || !isSameRouteRecord(to, from) || !oldInstance)) {\n                (to.enterCallbacks[name] || []).forEach(callback => callback(instance));\n            }\n        }, { flush: 'post' });\n        return () => {\n            const route = routeToDisplay.value;\n            // we need the value at the time we render because when we unmount, we\n            // navigated to a different location so the value is different\n            const currentName = props.name;\n            const matchedRoute = matchedRouteRef.value;\n            const ViewComponent = matchedRoute && matchedRoute.components[currentName];\n            if (!ViewComponent) {\n                return normalizeSlot(slots.default, { Component: ViewComponent, route });\n            }\n            // props from route configuration\n            const routePropsOption = matchedRoute.props[currentName];\n            const routeProps = routePropsOption\n                ? routePropsOption === true\n                    ? route.params\n                    : typeof routePropsOption === 'function'\n                        ? routePropsOption(route)\n                        : routePropsOption\n                : null;\n            const onVnodeUnmounted = vnode => {\n                // remove the instance reference to prevent leak\n                if (vnode.component.isUnmounted) {\n                    matchedRoute.instances[currentName] = null;\n                }\n            };\n            const component = h(ViewComponent, assign({}, routeProps, attrs, {\n                onVnodeUnmounted,\n                ref: viewRef,\n            }));\n            if (((process.env.NODE_ENV !== 'production') || __VUE_PROD_DEVTOOLS__) &&\n                isBrowser &&\n                component.ref) {\n                // TODO: can display if it's an alias, its props\n                const info = {\n                    depth: depth.value,\n                    name: matchedRoute.name,\n                    path: matchedRoute.path,\n                    meta: matchedRoute.meta,\n                };\n                const internalInstances = isArray(component.ref)\n                    ? component.ref.map(r => r.i)\n                    : [component.ref.i];\n                internalInstances.forEach(instance => {\n                    // @ts-expect-error\n                    instance.__vrv_devtools = info;\n                });\n            }\n            return (\n            // pass the vnode to the slot as a prop.\n            // h and <component :is=\"...\"> both accept vnodes\n            normalizeSlot(slots.default, { Component: component, route }) ||\n                component);\n        };\n    },\n});\nfunction normalizeSlot(slot, data) {\n    if (!slot)\n        return null;\n    const slotContent = slot(data);\n    return slotContent.length === 1 ? slotContent[0] : slotContent;\n}\n// export the public type for h/tsx inference\n// also to avoid inline import() in generated d.ts files\n/**\n * Component to display the current route the user is at.\n */\nconst RouterView = RouterViewImpl;\n// warn against deprecated usage with <transition> & <keep-alive>\n// due to functional component being no longer eager in Vue 3\nfunction warnDeprecatedUsage() {\n    const instance = getCurrentInstance();\n    const parentName = instance.parent && instance.parent.type.name;\n    const parentSubTreeType = instance.parent && instance.parent.subTree && instance.parent.subTree.type;\n    if (parentName &&\n        (parentName === 'KeepAlive' || parentName.includes('Transition')) &&\n        typeof parentSubTreeType === 'object' &&\n        parentSubTreeType.name === 'RouterView') {\n        const comp = parentName === 'KeepAlive' ? 'keep-alive' : 'transition';\n        warn(`<router-view> can no longer be used directly inside <transition> or <keep-alive>.\\n` +\n            `Use slot props instead:\\n\\n` +\n            `<router-view v-slot=\"{ Component }\">\\n` +\n            `  <${comp}>\\n` +\n            `    <component :is=\"Component\" />\\n` +\n            `  </${comp}>\\n` +\n            `</router-view>`);\n    }\n}\n\n/**\n * Copies a route location and removes any problematic properties that cannot be shown in devtools (e.g. Vue instances).\n *\n * @param routeLocation - routeLocation to format\n * @param tooltip - optional tooltip\n * @returns a copy of the routeLocation\n */\nfunction formatRouteLocation(routeLocation, tooltip) {\n    const copy = assign({}, routeLocation, {\n        // remove variables that can contain vue instances\n        matched: routeLocation.matched.map(matched => omit(matched, ['instances', 'children', 'aliasOf'])),\n    });\n    return {\n        _custom: {\n            type: null,\n            readOnly: true,\n            display: routeLocation.fullPath,\n            tooltip,\n            value: copy,\n        },\n    };\n}\nfunction formatDisplay(display) {\n    return {\n        _custom: {\n            display,\n        },\n    };\n}\n// to support multiple router instances\nlet routerId = 0;\nfunction addDevtools(app, router, matcher) {\n    // Take over router.beforeEach and afterEach\n    // make sure we are not registering the devtool twice\n    if (router.__hasDevtools)\n        return;\n    router.__hasDevtools = true;\n    // increment to support multiple router instances\n    const id = routerId++;\n    setupDevtoolsPlugin({\n        id: 'org.vuejs.router' + (id ? '.' + id : ''),\n        label: 'Vue Router',\n        packageName: 'vue-router',\n        homepage: 'https://router.vuejs.org',\n        logo: 'https://router.vuejs.org/logo.png',\n        componentStateTypes: ['Routing'],\n        app,\n    }, api => {\n        if (typeof api.now !== 'function') {\n            console.warn('[Vue Router]: You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html.');\n        }\n        // display state added by the router\n        api.on.inspectComponent((payload, ctx) => {\n            if (payload.instanceData) {\n                payload.instanceData.state.push({\n                    type: 'Routing',\n                    key: '$route',\n                    editable: false,\n                    value: formatRouteLocation(router.currentRoute.value, 'Current Route'),\n                });\n            }\n        });\n        // mark router-link as active and display tags on router views\n        api.on.visitComponentTree(({ treeNode: node, componentInstance }) => {\n            if (componentInstance.__vrv_devtools) {\n                const info = componentInstance.__vrv_devtools;\n                node.tags.push({\n                    label: (info.name ? `${info.name.toString()}: ` : '') + info.path,\n                    textColor: 0,\n                    tooltip: 'This component is rendered by &lt;router-view&gt;',\n                    backgroundColor: PINK_500,\n                });\n            }\n            // if multiple useLink are used\n            if (isArray(componentInstance.__vrl_devtools)) {\n                componentInstance.__devtoolsApi = api;\n                componentInstance.__vrl_devtools.forEach(devtoolsData => {\n                    let label = devtoolsData.route.path;\n                    let backgroundColor = ORANGE_400;\n                    let tooltip = '';\n                    let textColor = 0;\n                    if (devtoolsData.error) {\n                        label = devtoolsData.error;\n                        backgroundColor = RED_100;\n                        textColor = RED_700;\n                    }\n                    else if (devtoolsData.isExactActive) {\n                        backgroundColor = LIME_500;\n                        tooltip = 'This is exactly active';\n                    }\n                    else if (devtoolsData.isActive) {\n                        backgroundColor = BLUE_600;\n                        tooltip = 'This link is active';\n                    }\n                    node.tags.push({\n                        label,\n                        textColor,\n                        tooltip,\n                        backgroundColor,\n                    });\n                });\n            }\n        });\n        watch(router.currentRoute, () => {\n            // refresh active state\n            refreshRoutesView();\n            api.notifyComponentUpdate();\n            api.sendInspectorTree(routerInspectorId);\n            api.sendInspectorState(routerInspectorId);\n        });\n        const navigationsLayerId = 'router:navigations:' + id;\n        api.addTimelineLayer({\n            id: navigationsLayerId,\n            label: `Router${id ? ' ' + id : ''} Navigations`,\n            color: 0x40a8c4,\n        });\n        // const errorsLayerId = 'router:errors'\n        // api.addTimelineLayer({\n        //   id: errorsLayerId,\n        //   label: 'Router Errors',\n        //   color: 0xea5455,\n        // })\n        router.onError((error, to) => {\n            api.addTimelineEvent({\n                layerId: navigationsLayerId,\n                event: {\n                    title: 'Error during Navigation',\n                    subtitle: to.fullPath,\n                    logType: 'error',\n                    time: api.now(),\n                    data: { error },\n                    groupId: to.meta.__navigationId,\n                },\n            });\n        });\n        // attached to `meta` and used to group events\n        let navigationId = 0;\n        router.beforeEach((to, from) => {\n            const data = {\n                guard: formatDisplay('beforeEach'),\n                from: formatRouteLocation(from, 'Current Location during this navigation'),\n                to: formatRouteLocation(to, 'Target location'),\n            };\n            // Used to group navigations together, hide from devtools\n            Object.defineProperty(to.meta, '__navigationId', {\n                value: navigationId++,\n            });\n            api.addTimelineEvent({\n                layerId: navigationsLayerId,\n                event: {\n                    time: api.now(),\n                    title: 'Start of navigation',\n                    subtitle: to.fullPath,\n                    data,\n                    groupId: to.meta.__navigationId,\n                },\n            });\n        });\n        router.afterEach((to, from, failure) => {\n            const data = {\n                guard: formatDisplay('afterEach'),\n            };\n            if (failure) {\n                data.failure = {\n                    _custom: {\n                        type: Error,\n                        readOnly: true,\n                        display: failure ? failure.message : '',\n                        tooltip: 'Navigation Failure',\n                        value: failure,\n                    },\n                };\n                data.status = formatDisplay('❌');\n            }\n            else {\n                data.status = formatDisplay('✅');\n            }\n            // we set here to have the right order\n            data.from = formatRouteLocation(from, 'Current Location during this navigation');\n            data.to = formatRouteLocation(to, 'Target location');\n            api.addTimelineEvent({\n                layerId: navigationsLayerId,\n                event: {\n                    title: 'End of navigation',\n                    subtitle: to.fullPath,\n                    time: api.now(),\n                    data,\n                    logType: failure ? 'warning' : 'default',\n                    groupId: to.meta.__navigationId,\n                },\n            });\n        });\n        /**\n         * Inspector of Existing routes\n         */\n        const routerInspectorId = 'router-inspector:' + id;\n        api.addInspector({\n            id: routerInspectorId,\n            label: 'Routes' + (id ? ' ' + id : ''),\n            icon: 'book',\n            treeFilterPlaceholder: 'Search routes',\n        });\n        function refreshRoutesView() {\n            // the routes view isn't active\n            if (!activeRoutesPayload)\n                return;\n            const payload = activeRoutesPayload;\n            // children routes will appear as nested\n            let routes = matcher.getRoutes().filter(route => !route.parent ||\n                // these routes have a parent with no component which will not appear in the view\n                // therefore we still need to include them\n                !route.parent.record.components);\n            // reset match state to false\n            routes.forEach(resetMatchStateOnRouteRecord);\n            // apply a match state if there is a payload\n            if (payload.filter) {\n                routes = routes.filter(route => \n                // save matches state based on the payload\n                isRouteMatching(route, payload.filter.toLowerCase()));\n            }\n            // mark active routes\n            routes.forEach(route => markRouteRecordActive(route, router.currentRoute.value));\n            payload.rootNodes = routes.map(formatRouteRecordForInspector);\n        }\n        let activeRoutesPayload;\n        api.on.getInspectorTree(payload => {\n            activeRoutesPayload = payload;\n            if (payload.app === app && payload.inspectorId === routerInspectorId) {\n                refreshRoutesView();\n            }\n        });\n        /**\n         * Display information about the currently selected route record\n         */\n        api.on.getInspectorState(payload => {\n            if (payload.app === app && payload.inspectorId === routerInspectorId) {\n                const routes = matcher.getRoutes();\n                const route = routes.find(route => route.record.__vd_id === payload.nodeId);\n                if (route) {\n                    payload.state = {\n                        options: formatRouteRecordMatcherForStateInspector(route),\n                    };\n                }\n            }\n        });\n        api.sendInspectorTree(routerInspectorId);\n        api.sendInspectorState(routerInspectorId);\n    });\n}\nfunction modifierForKey(key) {\n    if (key.optional) {\n        return key.repeatable ? '*' : '?';\n    }\n    else {\n        return key.repeatable ? '+' : '';\n    }\n}\nfunction formatRouteRecordMatcherForStateInspector(route) {\n    const { record } = route;\n    const fields = [\n        { editable: false, key: 'path', value: record.path },\n    ];\n    if (record.name != null) {\n        fields.push({\n            editable: false,\n            key: 'name',\n            value: record.name,\n        });\n    }\n    fields.push({ editable: false, key: 'regexp', value: route.re });\n    if (route.keys.length) {\n        fields.push({\n            editable: false,\n            key: 'keys',\n            value: {\n                _custom: {\n                    type: null,\n                    readOnly: true,\n                    display: route.keys\n                        .map(key => `${key.name}${modifierForKey(key)}`)\n                        .join(' '),\n                    tooltip: 'Param keys',\n                    value: route.keys,\n                },\n            },\n        });\n    }\n    if (record.redirect != null) {\n        fields.push({\n            editable: false,\n            key: 'redirect',\n            value: record.redirect,\n        });\n    }\n    if (route.alias.length) {\n        fields.push({\n            editable: false,\n            key: 'aliases',\n            value: route.alias.map(alias => alias.record.path),\n        });\n    }\n    if (Object.keys(route.record.meta).length) {\n        fields.push({\n            editable: false,\n            key: 'meta',\n            value: route.record.meta,\n        });\n    }\n    fields.push({\n        key: 'score',\n        editable: false,\n        value: {\n            _custom: {\n                type: null,\n                readOnly: true,\n                display: route.score.map(score => score.join(', ')).join(' | '),\n                tooltip: 'Score used to sort routes',\n                value: route.score,\n            },\n        },\n    });\n    return fields;\n}\n/**\n * Extracted from tailwind palette\n */\nconst PINK_500 = 0xec4899;\nconst BLUE_600 = 0x2563eb;\nconst LIME_500 = 0x84cc16;\nconst CYAN_400 = 0x22d3ee;\nconst ORANGE_400 = 0xfb923c;\n// const GRAY_100 = 0xf4f4f5\nconst DARK = 0x666666;\nconst RED_100 = 0xfee2e2;\nconst RED_700 = 0xb91c1c;\nfunction formatRouteRecordForInspector(route) {\n    const tags = [];\n    const { record } = route;\n    if (record.name != null) {\n        tags.push({\n            label: String(record.name),\n            textColor: 0,\n            backgroundColor: CYAN_400,\n        });\n    }\n    if (record.aliasOf) {\n        tags.push({\n            label: 'alias',\n            textColor: 0,\n            backgroundColor: ORANGE_400,\n        });\n    }\n    if (route.__vd_match) {\n        tags.push({\n            label: 'matches',\n            textColor: 0,\n            backgroundColor: PINK_500,\n        });\n    }\n    if (route.__vd_exactActive) {\n        tags.push({\n            label: 'exact',\n            textColor: 0,\n            backgroundColor: LIME_500,\n        });\n    }\n    if (route.__vd_active) {\n        tags.push({\n            label: 'active',\n            textColor: 0,\n            backgroundColor: BLUE_600,\n        });\n    }\n    if (record.redirect) {\n        tags.push({\n            label: typeof record.redirect === 'string'\n                ? `redirect: ${record.redirect}`\n                : 'redirects',\n            textColor: 0xffffff,\n            backgroundColor: DARK,\n        });\n    }\n    // add an id to be able to select it. Using the `path` is not possible because\n    // empty path children would collide with their parents\n    let id = record.__vd_id;\n    if (id == null) {\n        id = String(routeRecordId++);\n        record.__vd_id = id;\n    }\n    return {\n        id,\n        label: record.path,\n        tags,\n        children: route.children.map(formatRouteRecordForInspector),\n    };\n}\n//  incremental id for route records and inspector state\nlet routeRecordId = 0;\nconst EXTRACT_REGEXP_RE = /^\\/(.*)\\/([a-z]*)$/;\nfunction markRouteRecordActive(route, currentRoute) {\n    // no route will be active if matched is empty\n    // reset the matching state\n    const isExactActive = currentRoute.matched.length &&\n        isSameRouteRecord(currentRoute.matched[currentRoute.matched.length - 1], route.record);\n    route.__vd_exactActive = route.__vd_active = isExactActive;\n    if (!isExactActive) {\n        route.__vd_active = currentRoute.matched.some(match => isSameRouteRecord(match, route.record));\n    }\n    route.children.forEach(childRoute => markRouteRecordActive(childRoute, currentRoute));\n}\nfunction resetMatchStateOnRouteRecord(route) {\n    route.__vd_match = false;\n    route.children.forEach(resetMatchStateOnRouteRecord);\n}\nfunction isRouteMatching(route, filter) {\n    const found = String(route.re).match(EXTRACT_REGEXP_RE);\n    route.__vd_match = false;\n    if (!found || found.length < 3) {\n        return false;\n    }\n    // use a regexp without $ at the end to match nested routes better\n    const nonEndingRE = new RegExp(found[1].replace(/\\$$/, ''), found[2]);\n    if (nonEndingRE.test(filter)) {\n        // mark children as matches\n        route.children.forEach(child => isRouteMatching(child, filter));\n        // exception case: `/`\n        if (route.record.path !== '/' || filter === '/') {\n            route.__vd_match = route.re.test(filter);\n            return true;\n        }\n        // hide the / route\n        return false;\n    }\n    const path = route.record.path.toLowerCase();\n    const decodedPath = decode(path);\n    // also allow partial matching on the path\n    if (!filter.startsWith('/') &&\n        (decodedPath.includes(filter) || path.includes(filter)))\n        return true;\n    if (decodedPath.startsWith(filter) || path.startsWith(filter))\n        return true;\n    if (route.record.name && String(route.record.name).includes(filter))\n        return true;\n    return route.children.some(child => isRouteMatching(child, filter));\n}\nfunction omit(obj, keys) {\n    const ret = {};\n    for (const key in obj) {\n        if (!keys.includes(key)) {\n            // @ts-expect-error\n            ret[key] = obj[key];\n        }\n    }\n    return ret;\n}\n\n/**\n * Creates a Router instance that can be used by a Vue app.\n *\n * @param options - {@link RouterOptions}\n */\nfunction createRouter(options) {\n    const matcher = createRouterMatcher(options.routes, options);\n    const parseQuery$1 = options.parseQuery || parseQuery;\n    const stringifyQuery$1 = options.stringifyQuery || stringifyQuery;\n    const routerHistory = options.history;\n    if ((process.env.NODE_ENV !== 'production') && !routerHistory)\n        throw new Error('Provide the \"history\" option when calling \"createRouter()\":' +\n            ' https://router.vuejs.org/api/interfaces/RouterOptions.html#history');\n    const beforeGuards = useCallbacks();\n    const beforeResolveGuards = useCallbacks();\n    const afterGuards = useCallbacks();\n    const currentRoute = shallowRef(START_LOCATION_NORMALIZED);\n    let pendingLocation = START_LOCATION_NORMALIZED;\n    // leave the scrollRestoration if no scrollBehavior is provided\n    if (isBrowser && options.scrollBehavior && 'scrollRestoration' in history) {\n        history.scrollRestoration = 'manual';\n    }\n    const normalizeParams = applyToParams.bind(null, paramValue => '' + paramValue);\n    const encodeParams = applyToParams.bind(null, encodeParam);\n    const decodeParams = \n    // @ts-expect-error: intentionally avoid the type check\n    applyToParams.bind(null, decode);\n    function addRoute(parentOrRoute, route) {\n        let parent;\n        let record;\n        if (isRouteName(parentOrRoute)) {\n            parent = matcher.getRecordMatcher(parentOrRoute);\n            if ((process.env.NODE_ENV !== 'production') && !parent) {\n                warn(`Parent route \"${String(parentOrRoute)}\" not found when adding child route`, route);\n            }\n            record = route;\n        }\n        else {\n            record = parentOrRoute;\n        }\n        return matcher.addRoute(record, parent);\n    }\n    function removeRoute(name) {\n        const recordMatcher = matcher.getRecordMatcher(name);\n        if (recordMatcher) {\n            matcher.removeRoute(recordMatcher);\n        }\n        else if ((process.env.NODE_ENV !== 'production')) {\n            warn(`Cannot remove non-existent route \"${String(name)}\"`);\n        }\n    }\n    function getRoutes() {\n        return matcher.getRoutes().map(routeMatcher => routeMatcher.record);\n    }\n    function hasRoute(name) {\n        return !!matcher.getRecordMatcher(name);\n    }\n    function resolve(rawLocation, currentLocation) {\n        // const resolve: Router['resolve'] = (rawLocation: RouteLocationRaw, currentLocation) => {\n        // const objectLocation = routerLocationAsObject(rawLocation)\n        // we create a copy to modify it later\n        currentLocation = assign({}, currentLocation || currentRoute.value);\n        if (typeof rawLocation === 'string') {\n            const locationNormalized = parseURL(parseQuery$1, rawLocation, currentLocation.path);\n            const matchedRoute = matcher.resolve({ path: locationNormalized.path }, currentLocation);\n            const href = routerHistory.createHref(locationNormalized.fullPath);\n            if ((process.env.NODE_ENV !== 'production')) {\n                if (href.startsWith('//'))\n                    warn(`Location \"${rawLocation}\" resolved to \"${href}\". A resolved location cannot start with multiple slashes.`);\n                else if (!matchedRoute.matched.length) {\n                    warn(`No match found for location with path \"${rawLocation}\"`);\n                }\n            }\n            // locationNormalized is always a new object\n            return assign(locationNormalized, matchedRoute, {\n                params: decodeParams(matchedRoute.params),\n                hash: decode(locationNormalized.hash),\n                redirectedFrom: undefined,\n                href,\n            });\n        }\n        if ((process.env.NODE_ENV !== 'production') && !isRouteLocation(rawLocation)) {\n            warn(`router.resolve() was passed an invalid location. This will fail in production.\\n- Location:`, rawLocation);\n            return resolve({});\n        }\n        let matcherLocation;\n        // path could be relative in object as well\n        if (rawLocation.path != null) {\n            if ((process.env.NODE_ENV !== 'production') &&\n                'params' in rawLocation &&\n                !('name' in rawLocation) &&\n                // @ts-expect-error: the type is never\n                Object.keys(rawLocation.params).length) {\n                warn(`Path \"${rawLocation.path}\" was passed with params but they will be ignored. Use a named route alongside params instead.`);\n            }\n            matcherLocation = assign({}, rawLocation, {\n                path: parseURL(parseQuery$1, rawLocation.path, currentLocation.path).path,\n            });\n        }\n        else {\n            // remove any nullish param\n            const targetParams = assign({}, rawLocation.params);\n            for (const key in targetParams) {\n                if (targetParams[key] == null) {\n                    delete targetParams[key];\n                }\n            }\n            // pass encoded values to the matcher, so it can produce encoded path and fullPath\n            matcherLocation = assign({}, rawLocation, {\n                params: encodeParams(targetParams),\n            });\n            // current location params are decoded, we need to encode them in case the\n            // matcher merges the params\n            currentLocation.params = encodeParams(currentLocation.params);\n        }\n        const matchedRoute = matcher.resolve(matcherLocation, currentLocation);\n        const hash = rawLocation.hash || '';\n        if ((process.env.NODE_ENV !== 'production') && hash && !hash.startsWith('#')) {\n            warn(`A \\`hash\\` should always start with the character \"#\". Replace \"${hash}\" with \"#${hash}\".`);\n        }\n        // the matcher might have merged current location params, so\n        // we need to run the decoding again\n        matchedRoute.params = normalizeParams(decodeParams(matchedRoute.params));\n        const fullPath = stringifyURL(stringifyQuery$1, assign({}, rawLocation, {\n            hash: encodeHash(hash),\n            path: matchedRoute.path,\n        }));\n        const href = routerHistory.createHref(fullPath);\n        if ((process.env.NODE_ENV !== 'production')) {\n            if (href.startsWith('//')) {\n                warn(`Location \"${rawLocation}\" resolved to \"${href}\". A resolved location cannot start with multiple slashes.`);\n            }\n            else if (!matchedRoute.matched.length) {\n                warn(`No match found for location with path \"${rawLocation.path != null ? rawLocation.path : rawLocation}\"`);\n            }\n        }\n        return assign({\n            fullPath,\n            // keep the hash encoded so fullPath is effectively path + encodedQuery +\n            // hash\n            hash,\n            query: \n            // if the user is using a custom query lib like qs, we might have\n            // nested objects, so we keep the query as is, meaning it can contain\n            // numbers at `$route.query`, but at the point, the user will have to\n            // use their own type anyway.\n            // https://github.com/vuejs/router/issues/328#issuecomment-649481567\n            stringifyQuery$1 === stringifyQuery\n                ? normalizeQuery(rawLocation.query)\n                : (rawLocation.query || {}),\n        }, matchedRoute, {\n            redirectedFrom: undefined,\n            href,\n        });\n    }\n    function locationAsObject(to) {\n        return typeof to === 'string'\n            ? parseURL(parseQuery$1, to, currentRoute.value.path)\n            : assign({}, to);\n    }\n    function checkCanceledNavigation(to, from) {\n        if (pendingLocation !== to) {\n            return createRouterError(8 /* ErrorTypes.NAVIGATION_CANCELLED */, {\n                from,\n                to,\n            });\n        }\n    }\n    function push(to) {\n        return pushWithRedirect(to);\n    }\n    function replace(to) {\n        return push(assign(locationAsObject(to), { replace: true }));\n    }\n    function handleRedirectRecord(to) {\n        const lastMatched = to.matched[to.matched.length - 1];\n        if (lastMatched && lastMatched.redirect) {\n            const { redirect } = lastMatched;\n            let newTargetLocation = typeof redirect === 'function' ? redirect(to) : redirect;\n            if (typeof newTargetLocation === 'string') {\n                newTargetLocation =\n                    newTargetLocation.includes('?') || newTargetLocation.includes('#')\n                        ? (newTargetLocation = locationAsObject(newTargetLocation))\n                        : // force empty params\n                            { path: newTargetLocation };\n                // @ts-expect-error: force empty params when a string is passed to let\n                // the router parse them again\n                newTargetLocation.params = {};\n            }\n            if ((process.env.NODE_ENV !== 'production') &&\n                newTargetLocation.path == null &&\n                !('name' in newTargetLocation)) {\n                warn(`Invalid redirect found:\\n${JSON.stringify(newTargetLocation, null, 2)}\\n when navigating to \"${to.fullPath}\". A redirect must contain a name or path. This will break in production.`);\n                throw new Error('Invalid redirect');\n            }\n            return assign({\n                query: to.query,\n                hash: to.hash,\n                // avoid transferring params if the redirect has a path\n                params: newTargetLocation.path != null ? {} : to.params,\n            }, newTargetLocation);\n        }\n    }\n    function pushWithRedirect(to, redirectedFrom) {\n        const targetLocation = (pendingLocation = resolve(to));\n        const from = currentRoute.value;\n        const data = to.state;\n        const force = to.force;\n        // to could be a string where `replace` is a function\n        const replace = to.replace === true;\n        const shouldRedirect = handleRedirectRecord(targetLocation);\n        if (shouldRedirect)\n            return pushWithRedirect(assign(locationAsObject(shouldRedirect), {\n                state: typeof shouldRedirect === 'object'\n                    ? assign({}, data, shouldRedirect.state)\n                    : data,\n                force,\n                replace,\n            }), \n            // keep original redirectedFrom if it exists\n            redirectedFrom || targetLocation);\n        // if it was a redirect we already called `pushWithRedirect` above\n        const toLocation = targetLocation;\n        toLocation.redirectedFrom = redirectedFrom;\n        let failure;\n        if (!force && isSameRouteLocation(stringifyQuery$1, from, targetLocation)) {\n            failure = createRouterError(16 /* ErrorTypes.NAVIGATION_DUPLICATED */, { to: toLocation, from });\n            // trigger scroll to allow scrolling to the same anchor\n            handleScroll(from, from, \n            // this is a push, the only way for it to be triggered from a\n            // history.listen is with a redirect, which makes it become a push\n            true, \n            // This cannot be the first navigation because the initial location\n            // cannot be manually navigated to\n            false);\n        }\n        return (failure ? Promise.resolve(failure) : navigate(toLocation, from))\n            .catch((error) => isNavigationFailure(error)\n            ? // navigation redirects still mark the router as ready\n                isNavigationFailure(error, 2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */)\n                    ? error\n                    : markAsReady(error) // also returns the error\n            : // reject any unknown error\n                triggerError(error, toLocation, from))\n            .then((failure) => {\n            if (failure) {\n                if (isNavigationFailure(failure, 2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */)) {\n                    if ((process.env.NODE_ENV !== 'production') &&\n                        // we are redirecting to the same location we were already at\n                        isSameRouteLocation(stringifyQuery$1, resolve(failure.to), toLocation) &&\n                        // and we have done it a couple of times\n                        redirectedFrom &&\n                        // @ts-expect-error: added only in dev\n                        (redirectedFrom._count = redirectedFrom._count\n                            ? // @ts-expect-error\n                                redirectedFrom._count + 1\n                            : 1) > 30) {\n                        warn(`Detected a possibly infinite redirection in a navigation guard when going from \"${from.fullPath}\" to \"${toLocation.fullPath}\". Aborting to avoid a Stack Overflow.\\n Are you always returning a new location within a navigation guard? That would lead to this error. Only return when redirecting or aborting, that should fix this. This might break in production if not fixed.`);\n                        return Promise.reject(new Error('Infinite redirect in navigation guard'));\n                    }\n                    return pushWithRedirect(\n                    // keep options\n                    assign({\n                        // preserve an existing replacement but allow the redirect to override it\n                        replace,\n                    }, locationAsObject(failure.to), {\n                        state: typeof failure.to === 'object'\n                            ? assign({}, data, failure.to.state)\n                            : data,\n                        force,\n                    }), \n                    // preserve the original redirectedFrom if any\n                    redirectedFrom || toLocation);\n                }\n            }\n            else {\n                // if we fail we don't finalize the navigation\n                failure = finalizeNavigation(toLocation, from, true, replace, data);\n            }\n            triggerAfterEach(toLocation, from, failure);\n            return failure;\n        });\n    }\n    /**\n     * Helper to reject and skip all navigation guards if a new navigation happened\n     * @param to\n     * @param from\n     */\n    function checkCanceledNavigationAndReject(to, from) {\n        const error = checkCanceledNavigation(to, from);\n        return error ? Promise.reject(error) : Promise.resolve();\n    }\n    function runWithContext(fn) {\n        const app = installedApps.values().next().value;\n        // support Vue < 3.3\n        return app && typeof app.runWithContext === 'function'\n            ? app.runWithContext(fn)\n            : fn();\n    }\n    // TODO: refactor the whole before guards by internally using router.beforeEach\n    function navigate(to, from) {\n        let guards;\n        const [leavingRecords, updatingRecords, enteringRecords] = extractChangingRecords(to, from);\n        // all components here have been resolved once because we are leaving\n        guards = extractComponentsGuards(leavingRecords.reverse(), 'beforeRouteLeave', to, from);\n        // leavingRecords is already reversed\n        for (const record of leavingRecords) {\n            record.leaveGuards.forEach(guard => {\n                guards.push(guardToPromiseFn(guard, to, from));\n            });\n        }\n        const canceledNavigationCheck = checkCanceledNavigationAndReject.bind(null, to, from);\n        guards.push(canceledNavigationCheck);\n        // run the queue of per route beforeRouteLeave guards\n        return (runGuardQueue(guards)\n            .then(() => {\n            // check global guards beforeEach\n            guards = [];\n            for (const guard of beforeGuards.list()) {\n                guards.push(guardToPromiseFn(guard, to, from));\n            }\n            guards.push(canceledNavigationCheck);\n            return runGuardQueue(guards);\n        })\n            .then(() => {\n            // check in components beforeRouteUpdate\n            guards = extractComponentsGuards(updatingRecords, 'beforeRouteUpdate', to, from);\n            for (const record of updatingRecords) {\n                record.updateGuards.forEach(guard => {\n                    guards.push(guardToPromiseFn(guard, to, from));\n                });\n            }\n            guards.push(canceledNavigationCheck);\n            // run the queue of per route beforeEnter guards\n            return runGuardQueue(guards);\n        })\n            .then(() => {\n            // check the route beforeEnter\n            guards = [];\n            for (const record of enteringRecords) {\n                // do not trigger beforeEnter on reused views\n                if (record.beforeEnter) {\n                    if (isArray(record.beforeEnter)) {\n                        for (const beforeEnter of record.beforeEnter)\n                            guards.push(guardToPromiseFn(beforeEnter, to, from));\n                    }\n                    else {\n                        guards.push(guardToPromiseFn(record.beforeEnter, to, from));\n                    }\n                }\n            }\n            guards.push(canceledNavigationCheck);\n            // run the queue of per route beforeEnter guards\n            return runGuardQueue(guards);\n        })\n            .then(() => {\n            // NOTE: at this point to.matched is normalized and does not contain any () => Promise<Component>\n            // clear existing enterCallbacks, these are added by extractComponentsGuards\n            to.matched.forEach(record => (record.enterCallbacks = {}));\n            // check in-component beforeRouteEnter\n            guards = extractComponentsGuards(enteringRecords, 'beforeRouteEnter', to, from, runWithContext);\n            guards.push(canceledNavigationCheck);\n            // run the queue of per route beforeEnter guards\n            return runGuardQueue(guards);\n        })\n            .then(() => {\n            // check global guards beforeResolve\n            guards = [];\n            for (const guard of beforeResolveGuards.list()) {\n                guards.push(guardToPromiseFn(guard, to, from));\n            }\n            guards.push(canceledNavigationCheck);\n            return runGuardQueue(guards);\n        })\n            // catch any navigation canceled\n            .catch(err => isNavigationFailure(err, 8 /* ErrorTypes.NAVIGATION_CANCELLED */)\n            ? err\n            : Promise.reject(err)));\n    }\n    function triggerAfterEach(to, from, failure) {\n        // navigation is confirmed, call afterGuards\n        // TODO: wrap with error handlers\n        afterGuards\n            .list()\n            .forEach(guard => runWithContext(() => guard(to, from, failure)));\n    }\n    /**\n     * - Cleans up any navigation guards\n     * - Changes the url if necessary\n     * - Calls the scrollBehavior\n     */\n    function finalizeNavigation(toLocation, from, isPush, replace, data) {\n        // a more recent navigation took place\n        const error = checkCanceledNavigation(toLocation, from);\n        if (error)\n            return error;\n        // only consider as push if it's not the first navigation\n        const isFirstNavigation = from === START_LOCATION_NORMALIZED;\n        const state = !isBrowser ? {} : history.state;\n        // change URL only if the user did a push/replace and if it's not the initial navigation because\n        // it's just reflecting the url\n        if (isPush) {\n            // on the initial navigation, we want to reuse the scroll position from\n            // history state if it exists\n            if (replace || isFirstNavigation)\n                routerHistory.replace(toLocation.fullPath, assign({\n                    scroll: isFirstNavigation && state && state.scroll,\n                }, data));\n            else\n                routerHistory.push(toLocation.fullPath, data);\n        }\n        // accept current navigation\n        currentRoute.value = toLocation;\n        handleScroll(toLocation, from, isPush, isFirstNavigation);\n        markAsReady();\n    }\n    let removeHistoryListener;\n    // attach listener to history to trigger navigations\n    function setupListeners() {\n        // avoid setting up listeners twice due to an invalid first navigation\n        if (removeHistoryListener)\n            return;\n        removeHistoryListener = routerHistory.listen((to, _from, info) => {\n            if (!router.listening)\n                return;\n            // cannot be a redirect route because it was in history\n            const toLocation = resolve(to);\n            // due to dynamic routing, and to hash history with manual navigation\n            // (manually changing the url or calling history.hash = '#/somewhere'),\n            // there could be a redirect record in history\n            const shouldRedirect = handleRedirectRecord(toLocation);\n            if (shouldRedirect) {\n                pushWithRedirect(assign(shouldRedirect, { replace: true, force: true }), toLocation).catch(noop);\n                return;\n            }\n            pendingLocation = toLocation;\n            const from = currentRoute.value;\n            // TODO: should be moved to web history?\n            if (isBrowser) {\n                saveScrollPosition(getScrollKey(from.fullPath, info.delta), computeScrollPosition());\n            }\n            navigate(toLocation, from)\n                .catch((error) => {\n                if (isNavigationFailure(error, 4 /* ErrorTypes.NAVIGATION_ABORTED */ | 8 /* ErrorTypes.NAVIGATION_CANCELLED */)) {\n                    return error;\n                }\n                if (isNavigationFailure(error, 2 /* ErrorTypes.NAVIGATION_GUARD_REDIRECT */)) {\n                    // Here we could call if (info.delta) routerHistory.go(-info.delta,\n                    // false) but this is bug prone as we have no way to wait the\n                    // navigation to be finished before calling pushWithRedirect. Using\n                    // a setTimeout of 16ms seems to work but there is no guarantee for\n                    // it to work on every browser. So instead we do not restore the\n                    // history entry and trigger a new navigation as requested by the\n                    // navigation guard.\n                    // the error is already handled by router.push we just want to avoid\n                    // logging the error\n                    pushWithRedirect(assign(locationAsObject(error.to), {\n                        force: true,\n                    }), toLocation\n                    // avoid an uncaught rejection, let push call triggerError\n                    )\n                        .then(failure => {\n                        // manual change in hash history #916 ending up in the URL not\n                        // changing, but it was changed by the manual url change, so we\n                        // need to manually change it ourselves\n                        if (isNavigationFailure(failure, 4 /* ErrorTypes.NAVIGATION_ABORTED */ |\n                            16 /* ErrorTypes.NAVIGATION_DUPLICATED */) &&\n                            !info.delta &&\n                            info.type === NavigationType.pop) {\n                            routerHistory.go(-1, false);\n                        }\n                    })\n                        .catch(noop);\n                    // avoid the then branch\n                    return Promise.reject();\n                }\n                // do not restore history on unknown direction\n                if (info.delta) {\n                    routerHistory.go(-info.delta, false);\n                }\n                // unrecognized error, transfer to the global handler\n                return triggerError(error, toLocation, from);\n            })\n                .then((failure) => {\n                failure =\n                    failure ||\n                        finalizeNavigation(\n                        // after navigation, all matched components are resolved\n                        toLocation, from, false);\n                // revert the navigation\n                if (failure) {\n                    if (info.delta &&\n                        // a new navigation has been triggered, so we do not want to revert, that will change the current history\n                        // entry while a different route is displayed\n                        !isNavigationFailure(failure, 8 /* ErrorTypes.NAVIGATION_CANCELLED */)) {\n                        routerHistory.go(-info.delta, false);\n                    }\n                    else if (info.type === NavigationType.pop &&\n                        isNavigationFailure(failure, 4 /* ErrorTypes.NAVIGATION_ABORTED */ | 16 /* ErrorTypes.NAVIGATION_DUPLICATED */)) {\n                        // manual change in hash history #916\n                        // it's like a push but lacks the information of the direction\n                        routerHistory.go(-1, false);\n                    }\n                }\n                triggerAfterEach(toLocation, from, failure);\n            })\n                // avoid warnings in the console about uncaught rejections, they are logged by triggerErrors\n                .catch(noop);\n        });\n    }\n    // Initialization and Errors\n    let readyHandlers = useCallbacks();\n    let errorListeners = useCallbacks();\n    let ready;\n    /**\n     * Trigger errorListeners added via onError and throws the error as well\n     *\n     * @param error - error to throw\n     * @param to - location we were navigating to when the error happened\n     * @param from - location we were navigating from when the error happened\n     * @returns the error as a rejected promise\n     */\n    function triggerError(error, to, from) {\n        markAsReady(error);\n        const list = errorListeners.list();\n        if (list.length) {\n            list.forEach(handler => handler(error, to, from));\n        }\n        else {\n            if ((process.env.NODE_ENV !== 'production')) {\n                warn('uncaught error during route navigation:');\n            }\n            console.error(error);\n        }\n        // reject the error no matter there were error listeners or not\n        return Promise.reject(error);\n    }\n    function isReady() {\n        if (ready && currentRoute.value !== START_LOCATION_NORMALIZED)\n            return Promise.resolve();\n        return new Promise((resolve, reject) => {\n            readyHandlers.add([resolve, reject]);\n        });\n    }\n    function markAsReady(err) {\n        if (!ready) {\n            // still not ready if an error happened\n            ready = !err;\n            setupListeners();\n            readyHandlers\n                .list()\n                .forEach(([resolve, reject]) => (err ? reject(err) : resolve()));\n            readyHandlers.reset();\n        }\n        return err;\n    }\n    // Scroll behavior\n    function handleScroll(to, from, isPush, isFirstNavigation) {\n        const { scrollBehavior } = options;\n        if (!isBrowser || !scrollBehavior)\n            return Promise.resolve();\n        const scrollPosition = (!isPush && getSavedScrollPosition(getScrollKey(to.fullPath, 0))) ||\n            ((isFirstNavigation || !isPush) &&\n                history.state &&\n                history.state.scroll) ||\n            null;\n        return nextTick()\n            .then(() => scrollBehavior(to, from, scrollPosition))\n            .then(position => position && scrollToPosition(position))\n            .catch(err => triggerError(err, to, from));\n    }\n    const go = (delta) => routerHistory.go(delta);\n    let started;\n    const installedApps = new Set();\n    const router = {\n        currentRoute,\n        listening: true,\n        addRoute,\n        removeRoute,\n        clearRoutes: matcher.clearRoutes,\n        hasRoute,\n        getRoutes,\n        resolve,\n        options,\n        push,\n        replace,\n        go,\n        back: () => go(-1),\n        forward: () => go(1),\n        beforeEach: beforeGuards.add,\n        beforeResolve: beforeResolveGuards.add,\n        afterEach: afterGuards.add,\n        onError: errorListeners.add,\n        isReady,\n        install(app) {\n            const router = this;\n            app.component('RouterLink', RouterLink);\n            app.component('RouterView', RouterView);\n            app.config.globalProperties.$router = router;\n            Object.defineProperty(app.config.globalProperties, '$route', {\n                enumerable: true,\n                get: () => unref(currentRoute),\n            });\n            // this initial navigation is only necessary on client, on server it doesn't\n            // make sense because it will create an extra unnecessary navigation and could\n            // lead to problems\n            if (isBrowser &&\n                // used for the initial navigation client side to avoid pushing\n                // multiple times when the router is used in multiple apps\n                !started &&\n                currentRoute.value === START_LOCATION_NORMALIZED) {\n                // see above\n                started = true;\n                push(routerHistory.location).catch(err => {\n                    if ((process.env.NODE_ENV !== 'production'))\n                        warn('Unexpected error when starting the router:', err);\n                });\n            }\n            const reactiveRoute = {};\n            for (const key in START_LOCATION_NORMALIZED) {\n                Object.defineProperty(reactiveRoute, key, {\n                    get: () => currentRoute.value[key],\n                    enumerable: true,\n                });\n            }\n            app.provide(routerKey, router);\n            app.provide(routeLocationKey, shallowReactive(reactiveRoute));\n            app.provide(routerViewLocationKey, currentRoute);\n            const unmountApp = app.unmount;\n            installedApps.add(app);\n            app.unmount = function () {\n                installedApps.delete(app);\n                // the router is not attached to an app anymore\n                if (installedApps.size < 1) {\n                    // invalidate the current navigation\n                    pendingLocation = START_LOCATION_NORMALIZED;\n                    removeHistoryListener && removeHistoryListener();\n                    removeHistoryListener = null;\n                    currentRoute.value = START_LOCATION_NORMALIZED;\n                    started = false;\n                    ready = false;\n                }\n                unmountApp();\n            };\n            // TODO: this probably needs to be updated so it can be used by vue-termui\n            if (((process.env.NODE_ENV !== 'production') || __VUE_PROD_DEVTOOLS__) && isBrowser) {\n                addDevtools(app, router, matcher);\n            }\n        },\n    };\n    // TODO: type this as NavigationGuardReturn or similar instead of any\n    function runGuardQueue(guards) {\n        return guards.reduce((promise, guard) => promise.then(() => runWithContext(guard)), Promise.resolve());\n    }\n    return router;\n}\nfunction extractChangingRecords(to, from) {\n    const leavingRecords = [];\n    const updatingRecords = [];\n    const enteringRecords = [];\n    const len = Math.max(from.matched.length, to.matched.length);\n    for (let i = 0; i < len; i++) {\n        const recordFrom = from.matched[i];\n        if (recordFrom) {\n            if (to.matched.find(record => isSameRouteRecord(record, recordFrom)))\n                updatingRecords.push(recordFrom);\n            else\n                leavingRecords.push(recordFrom);\n        }\n        const recordTo = to.matched[i];\n        if (recordTo) {\n            // the type doesn't matter because we are comparing per reference\n            if (!from.matched.find(record => isSameRouteRecord(record, recordTo))) {\n                enteringRecords.push(recordTo);\n            }\n        }\n    }\n    return [leavingRecords, updatingRecords, enteringRecords];\n}\n\n/**\n * Returns the router instance. Equivalent to using `$router` inside\n * templates.\n */\nfunction useRouter() {\n    return inject(routerKey);\n}\n/**\n * Returns the current route location. Equivalent to using `$route` inside\n * templates.\n */\nfunction useRoute(_name) {\n    return inject(routeLocationKey);\n}\n\nexport { NavigationFailureType, RouterLink, RouterView, START_LOCATION_NORMALIZED as START_LOCATION, createMemoryHistory, createRouter, createRouterMatcher, createWebHashHistory, createWebHistory, isNavigationFailure, loadRouteLocation, matchedRouteKey, onBeforeRouteLeave, onBeforeRouteUpdate, parseQuery, routeLocationKey, routerKey, routerViewLocationKey, stringifyQuery, useLink, useRoute, useRouter, viewDepthKey };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA,AAQA,IAAM,YAAY,OAAO,aAAa;AAQtC,0BAA0B,WAAW;AACjC,SAAQ,OAAO,cAAc,YACzB,iBAAiB,aACjB,WAAW,aACX,eAAe;AAAA;AAEvB,oBAAoB,KAAK;AACrB,SAAQ,IAAI,cACR,IAAI,OAAO,iBAAiB,YAG3B,IAAI,WAAW,iBAAiB,IAAI;AAAA;AAE7C,IAAM,SAAS,OAAO;AACtB,uBAAuB,IAAI,QAAQ;AAC/B,QAAM,YAAY;AAClB,aAAW,OAAO,QAAQ;AACtB,UAAM,QAAQ,OAAO;AACrB,cAAU,OAAO,QAAQ,SACnB,MAAM,IAAI,MACV,GAAG;AAAA;AAEb,SAAO;AAAA;AAEX,IAAM,OAAO,MAAM;AAAA;AAKnB,IAAM,UAAU,MAAM;AAEtB,cAAc,KAAK;AAEf,QAAM,OAAO,MAAM,KAAK,WAAW,MAAM;AACzC,UAAQ,KAAK,MAAM,SAAS,CAAC,wBAAwB,KAAK,OAAO;AAAA;AAsBrE,IAAM,UAAU;AAChB,IAAM,eAAe;AACrB,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,QAAQ;AACd,IAAM,UAAU;AAehB,IAAM,sBAAsB;AAC5B,IAAM,uBAAuB;AAC7B,IAAM,eAAe;AACrB,IAAM,kBAAkB;AACxB,IAAM,oBAAoB;AAC1B,IAAM,cAAc;AACpB,IAAM,qBAAqB;AAC3B,IAAM,eAAe;AASrB,sBAAsB,MAAM;AACxB,SAAO,UAAU,KAAK,MACjB,QAAQ,aAAa,KACrB,QAAQ,qBAAqB,KAC7B,QAAQ,sBAAsB;AAAA;AAQvC,oBAAoB,MAAM;AACtB,SAAO,aAAa,MACf,QAAQ,mBAAmB,KAC3B,QAAQ,oBAAoB,KAC5B,QAAQ,cAAc;AAAA;AAS/B,0BAA0B,MAAM;AAC5B,SAAQ,aAAa,MAEhB,QAAQ,SAAS,OACjB,QAAQ,cAAc,KACtB,QAAQ,SAAS,OACjB,QAAQ,cAAc,OACtB,QAAQ,iBAAiB,KACzB,QAAQ,mBAAmB,KAC3B,QAAQ,oBAAoB,KAC5B,QAAQ,cAAc;AAAA;AAO/B,wBAAwB,MAAM;AAC1B,SAAO,iBAAiB,MAAM,QAAQ,UAAU;AAAA;AAQpD,oBAAoB,MAAM;AACtB,SAAO,aAAa,MAAM,QAAQ,SAAS,OAAO,QAAQ,OAAO;AAAA;AAWrE,qBAAqB,MAAM;AACvB,SAAO,QAAQ,OAAO,KAAK,WAAW,MAAM,QAAQ,UAAU;AAAA;AASlE,gBAAgB,MAAM;AAClB,MAAI;AACA,WAAO,mBAAmB,KAAK;AAAA,WAE5B,KAAP;AACI,IAA2C,KAAK,mBAAmB;AAAA;AAEvE,SAAO,KAAK;AAAA;AAGhB,IAAM,oBAAoB;AAC1B,IAAM,sBAAsB,CAAC,SAAS,KAAK,QAAQ,mBAAmB;AAUtE,kBAAkB,aAAY,WAAU,kBAAkB,KAAK;AAC3D,MAAI,MAAM,QAAQ,IAAI,eAAe,IAAI,OAAO;AAGhD,QAAM,UAAU,UAAS,QAAQ;AACjC,MAAI,YAAY,UAAS,QAAQ;AAEjC,MAAI,UAAU,aAAa,WAAW,GAAG;AACrC,gBAAY;AAAA;AAEhB,MAAI,YAAY,IAAI;AAChB,WAAO,UAAS,MAAM,GAAG;AACzB,mBAAe,UAAS,MAAM,YAAY,GAAG,UAAU,KAAK,UAAU,UAAS;AAC/E,YAAQ,YAAW;AAAA;AAEvB,MAAI,UAAU,IAAI;AACd,WAAO,QAAQ,UAAS,MAAM,GAAG;AAEjC,WAAO,UAAS,MAAM,SAAS,UAAS;AAAA;AAG5C,SAAO,oBAAoB,QAAQ,OAAO,OAAO,WAAU;AAE3D,SAAO;AAAA,IACH,UAAU,OAAQ,iBAAgB,OAAO,eAAe;AAAA,IACxD;AAAA,IACA;AAAA,IACA,MAAM,OAAO;AAAA;AAAA;AASrB,sBAAsB,iBAAgB,WAAU;AAC5C,QAAM,QAAQ,UAAS,QAAQ,gBAAe,UAAS,SAAS;AAChE,SAAO,UAAS,OAAQ,UAAS,OAAO,QAAS,WAAS,QAAQ;AAAA;AAQtE,mBAAmB,UAAU,MAAM;AAE/B,MAAI,CAAC,QAAQ,CAAC,SAAS,cAAc,WAAW,KAAK;AACjD,WAAO;AACX,SAAO,SAAS,MAAM,KAAK,WAAW;AAAA;AAW1C,6BAA6B,iBAAgB,GAAG,GAAG;AAC/C,QAAM,aAAa,EAAE,QAAQ,SAAS;AACtC,QAAM,aAAa,EAAE,QAAQ,SAAS;AACtC,SAAQ,aAAa,MACjB,eAAe,cACf,kBAAkB,EAAE,QAAQ,aAAa,EAAE,QAAQ,gBACnD,0BAA0B,EAAE,QAAQ,EAAE,WACtC,gBAAe,EAAE,WAAW,gBAAe,EAAE,UAC7C,EAAE,SAAS,EAAE;AAAA;AASrB,2BAA2B,GAAG,GAAG;AAI7B,SAAQ,GAAE,WAAW,OAAQ,GAAE,WAAW;AAAA;AAE9C,mCAAmC,GAAG,GAAG;AACrC,MAAI,OAAO,KAAK,GAAG,WAAW,OAAO,KAAK,GAAG;AACzC,WAAO;AACX,aAAW,OAAO,GAAG;AACjB,QAAI,CAAC,+BAA+B,EAAE,MAAM,EAAE;AAC1C,aAAO;AAAA;AAEf,SAAO;AAAA;AAEX,wCAAwC,GAAG,GAAG;AAC1C,SAAO,QAAQ,KACT,kBAAkB,GAAG,KACrB,QAAQ,KACJ,kBAAkB,GAAG,KACrB,MAAM;AAAA;AASpB,2BAA2B,GAAG,GAAG;AAC7B,SAAO,QAAQ,KACT,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,CAAC,OAAO,MAAM,UAAU,EAAE,MAC3D,EAAE,WAAW,KAAK,EAAE,OAAO;AAAA;AAQrC,6BAA6B,IAAI,MAAM;AACnC,MAAI,GAAG,WAAW;AACd,WAAO;AACX,MAA+C,CAAC,KAAK,WAAW,MAAM;AAClE,SAAK,mFAAmF,aAAa,gCAAgC;AACrI,WAAO;AAAA;AAEX,MAAI,CAAC;AACD,WAAO;AACX,QAAM,eAAe,KAAK,MAAM;AAChC,QAAM,aAAa,GAAG,MAAM;AAC5B,QAAM,gBAAgB,WAAW,WAAW,SAAS;AAGrD,MAAI,kBAAkB,QAAQ,kBAAkB,KAAK;AACjD,eAAW,KAAK;AAAA;AAEpB,MAAI,WAAW,aAAa,SAAS;AACrC,MAAI;AACJ,MAAI;AACJ,OAAK,aAAa,GAAG,aAAa,WAAW,QAAQ,cAAc;AAC/D,cAAU,WAAW;AAErB,QAAI,YAAY;AACZ;AAEJ,QAAI,YAAY,MAAM;AAElB,UAAI,WAAW;AACX;AAAA;AAKJ;AAAA;AAER,SAAQ,aAAa,MAAM,GAAG,UAAU,KAAK,OACzC,MACA,WAAW,MAAM,YAAY,KAAK;AAAA;AAiB1C,IAAM,4BAA4B;AAAA,EAC9B,MAAM;AAAA,EAEN,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,MAAM;AAAA,EACN,UAAU;AAAA,EACV,SAAS;AAAA,EACT,MAAM;AAAA,EACN,gBAAgB;AAAA;AAGpB,IAAI;AACJ,AAAC,UAAU,iBAAgB;AACvB,kBAAe,SAAS;AACxB,kBAAe,UAAU;AAAA,GAC1B,kBAAmB,kBAAiB;AACvC,IAAI;AACJ,AAAC,UAAU,sBAAqB;AAC5B,uBAAoB,UAAU;AAC9B,uBAAoB,aAAa;AACjC,uBAAoB,aAAa;AAAA,GAClC,uBAAwB,uBAAsB;AAIjD,IAAM,QAAQ;AAQd,uBAAuB,MAAM;AACzB,MAAI,CAAC,MAAM;AACP,QAAI,WAAW;AAEX,YAAM,SAAS,SAAS,cAAc;AACtC,aAAQ,UAAU,OAAO,aAAa,WAAY;AAElD,aAAO,KAAK,QAAQ,mBAAmB;AAAA,WAEtC;AACD,aAAO;AAAA;AAAA;AAMf,MAAI,KAAK,OAAO,OAAO,KAAK,OAAO;AAC/B,WAAO,MAAM;AAGjB,SAAO,oBAAoB;AAAA;AAG/B,IAAM,iBAAiB;AACvB,oBAAoB,MAAM,WAAU;AAChC,SAAO,KAAK,QAAQ,gBAAgB,OAAO;AAAA;AAG/C,4BAA4B,IAAI,QAAQ;AACpC,QAAM,UAAU,SAAS,gBAAgB;AACzC,QAAM,SAAS,GAAG;AAClB,SAAO;AAAA,IACH,UAAU,OAAO;AAAA,IACjB,MAAM,OAAO,OAAO,QAAQ,OAAQ,QAAO,QAAQ;AAAA,IACnD,KAAK,OAAO,MAAM,QAAQ,MAAO,QAAO,OAAO;AAAA;AAAA;AAGvD,IAAM,wBAAwB,MAAO;AAAA,EACjC,MAAM,OAAO;AAAA,EACb,KAAK,OAAO;AAAA;AAEhB,0BAA0B,UAAU;AAChC,MAAI;AACJ,MAAI,QAAQ,UAAU;AAClB,UAAM,aAAa,SAAS;AAC5B,UAAM,eAAe,OAAO,eAAe,YAAY,WAAW,WAAW;AAsB7E,QAA+C,OAAO,SAAS,OAAO,UAAU;AAC5E,UAAI,CAAC,gBAAgB,CAAC,SAAS,eAAe,SAAS,GAAG,MAAM,KAAK;AACjE,YAAI;AACA,gBAAM,UAAU,SAAS,cAAc,SAAS;AAChD,cAAI,gBAAgB,SAAS;AACzB,iBAAK,iBAAiB,SAAS,wDAAwD,SAAS;AAEhG;AAAA;AAAA,iBAGD,KAAP;AACI,eAAK,iBAAiB,SAAS;AAE/B;AAAA;AAAA;AAAA;AAIZ,UAAM,KAAK,OAAO,eAAe,WAC3B,eACI,SAAS,eAAe,WAAW,MAAM,MACzC,SAAS,cAAc,cAC3B;AACN,QAAI,CAAC,IAAI;AACL,MACI,KAAK,yCAAyC,SAAS;AAC3D;AAAA;AAEJ,sBAAkB,mBAAmB,IAAI;AAAA,SAExC;AACD,sBAAkB;AAAA;AAEtB,MAAI,oBAAoB,SAAS,gBAAgB;AAC7C,WAAO,SAAS;AAAA,OACf;AACD,WAAO,SAAS,gBAAgB,QAAQ,OAAO,gBAAgB,OAAO,OAAO,SAAS,gBAAgB,OAAO,OAAO,gBAAgB,MAAM,OAAO;AAAA;AAAA;AAGzJ,sBAAsB,MAAM,OAAO;AAC/B,QAAM,WAAW,QAAQ,QAAQ,QAAQ,MAAM,WAAW,QAAQ;AAClE,SAAO,WAAW;AAAA;AAEtB,IAAM,kBAAkB,IAAI;AAC5B,4BAA4B,KAAK,gBAAgB;AAC7C,kBAAgB,IAAI,KAAK;AAAA;AAE7B,gCAAgC,KAAK;AACjC,QAAM,SAAS,gBAAgB,IAAI;AAEnC,kBAAgB,OAAO;AACvB,SAAO;AAAA;AAkBX,IAAI,qBAAqB,MAAM,SAAS,WAAW,OAAO,SAAS;AAMnE,+BAA+B,MAAM,WAAU;AAC3C,QAAM,EAAE,UAAU,QAAQ,SAAS;AAEnC,QAAM,UAAU,KAAK,QAAQ;AAC7B,MAAI,UAAU,IAAI;AACd,QAAI,WAAW,KAAK,SAAS,KAAK,MAAM,YAClC,KAAK,MAAM,SAAS,SACpB;AACN,QAAI,eAAe,KAAK,MAAM;AAE9B,QAAI,aAAa,OAAO;AACpB,qBAAe,MAAM;AACzB,WAAO,UAAU,cAAc;AAAA;AAEnC,QAAM,OAAO,UAAU,UAAU;AACjC,SAAO,OAAO,SAAS;AAAA;AAE3B,6BAA6B,MAAM,cAAc,iBAAiB,SAAS;AACvE,MAAI,YAAY;AAChB,MAAI,YAAY;AAGhB,MAAI,aAAa;AACjB,QAAM,kBAAkB,CAAC,EAAE,YAAa;AACpC,UAAM,KAAK,sBAAsB,MAAM;AACvC,UAAM,OAAO,gBAAgB;AAC7B,UAAM,YAAY,aAAa;AAC/B,QAAI,QAAQ;AACZ,QAAI,OAAO;AACP,sBAAgB,QAAQ;AACxB,mBAAa,QAAQ;AAErB,UAAI,cAAc,eAAe,MAAM;AACnC,qBAAa;AACb;AAAA;AAEJ,cAAQ,YAAY,MAAM,WAAW,UAAU,WAAW;AAAA,WAEzD;AACD,cAAQ;AAAA;AAOZ,cAAU,QAAQ,cAAY;AAC1B,eAAS,gBAAgB,OAAO,MAAM;AAAA,QAClC;AAAA,QACA,MAAM,eAAe;AAAA,QACrB,WAAW,QACL,QAAQ,IACJ,oBAAoB,UACpB,oBAAoB,OACxB,oBAAoB;AAAA;AAAA;AAAA;AAItC,4BAA0B;AACtB,iBAAa,gBAAgB;AAAA;AAEjC,kBAAgB,UAAU;AAEtB,cAAU,KAAK;AACf,UAAM,WAAW,MAAM;AACnB,YAAM,QAAQ,UAAU,QAAQ;AAChC,UAAI,QAAQ;AACR,kBAAU,OAAO,OAAO;AAAA;AAEhC,cAAU,KAAK;AACf,WAAO;AAAA;AAEX,kCAAgC;AAC5B,UAAM,EAAE,sBAAY;AACpB,QAAI,CAAC,SAAQ;AACT;AACJ,aAAQ,aAAa,OAAO,IAAI,SAAQ,OAAO,EAAE,QAAQ,4BAA4B;AAAA;AAEzF,qBAAmB;AACf,eAAW,YAAY;AACnB;AACJ,gBAAY;AACZ,WAAO,oBAAoB,YAAY;AACvC,WAAO,oBAAoB,gBAAgB;AAAA;AAG/C,SAAO,iBAAiB,YAAY;AAGpC,SAAO,iBAAiB,gBAAgB,sBAAsB;AAAA,IAC1D,SAAS;AAAA;AAEb,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA;AAAA;AAMR,oBAAoB,MAAM,SAAS,SAAS,WAAW,OAAO,gBAAgB,OAAO;AACjF,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,UAAU,OAAO,QAAQ;AAAA,IACzB,QAAQ,gBAAgB,0BAA0B;AAAA;AAAA;AAG1D,mCAAmC,MAAM;AACrC,QAAM,EAAE,mBAAS,wBAAa;AAE9B,QAAM,kBAAkB;AAAA,IACpB,OAAO,sBAAsB,MAAM;AAAA;AAEvC,QAAM,eAAe,EAAE,OAAO,SAAQ;AAEtC,MAAI,CAAC,aAAa,OAAO;AACrB,mBAAe,gBAAgB,OAAO;AAAA,MAClC,MAAM;AAAA,MACN,SAAS,gBAAgB;AAAA,MACzB,SAAS;AAAA,MAET,UAAU,SAAQ,SAAS;AAAA,MAC3B,UAAU;AAAA,MAGV,QAAQ;AAAA,OACT;AAAA;AAEP,0BAAwB,IAAI,OAAO,UAAS;AAUxC,UAAM,YAAY,KAAK,QAAQ;AAC/B,UAAM,MAAM,YAAY,KACjB,WAAS,QAAQ,SAAS,cAAc,UACrC,OACA,KAAK,MAAM,cAAc,KAC7B,uBAAuB,OAAO;AACpC,QAAI;AAGA,eAAQ,WAAU,iBAAiB,aAAa,OAAO,IAAI;AAC3D,mBAAa,QAAQ;AAAA,aAElB,KAAP;AACI,UAAK,MAAwC;AACzC,aAAK,iCAAiC;AAAA,aAErC;AACD,gBAAQ,MAAM;AAAA;AAGlB,gBAAS,WAAU,YAAY,UAAU;AAAA;AAAA;AAGjD,mBAAiB,IAAI,MAAM;AACvB,UAAM,QAAQ,OAAO,IAAI,SAAQ,OAAO,WAAW,aAAa,MAAM,MAEtE,IAAI,aAAa,MAAM,SAAS,OAAO,MAAM,EAAE,UAAU,aAAa,MAAM;AAC5E,mBAAe,IAAI,OAAO;AAC1B,oBAAgB,QAAQ;AAAA;AAE5B,gBAAc,IAAI,MAAM;AAGpB,UAAM,eAAe,OAAO,IAI5B,aAAa,OAAO,SAAQ,OAAO;AAAA,MAC/B,SAAS;AAAA,MACT,QAAQ;AAAA;AAEZ,QAA+C,CAAC,SAAQ,OAAO;AAC3D,WAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAIT,mBAAe,aAAa,SAAS,cAAc;AACnD,UAAM,QAAQ,OAAO,IAAI,WAAW,gBAAgB,OAAO,IAAI,OAAO,EAAE,UAAU,aAAa,WAAW,KAAK;AAC/G,mBAAe,IAAI,OAAO;AAC1B,oBAAgB,QAAQ;AAAA;AAE5B,SAAO;AAAA,IACH,UAAU;AAAA,IACV,OAAO;AAAA,IACP;AAAA,IACA;AAAA;AAAA;AAQR,0BAA0B,MAAM;AAC5B,SAAO,cAAc;AACrB,QAAM,oBAAoB,0BAA0B;AACpD,QAAM,mBAAmB,oBAAoB,MAAM,kBAAkB,OAAO,kBAAkB,UAAU,kBAAkB;AAC1H,cAAY,OAAO,mBAAmB,MAAM;AACxC,QAAI,CAAC;AACD,uBAAiB;AACrB,YAAQ,GAAG;AAAA;AAEf,QAAM,gBAAgB,OAAO;AAAA,IAEzB,UAAU;AAAA,IACV;AAAA,IACA;AAAA,IACA,YAAY,WAAW,KAAK,MAAM;AAAA,KACnC,mBAAmB;AACtB,SAAO,eAAe,eAAe,YAAY;AAAA,IAC7C,YAAY;AAAA,IACZ,KAAK,MAAM,kBAAkB,SAAS;AAAA;AAE1C,SAAO,eAAe,eAAe,SAAS;AAAA,IAC1C,YAAY;AAAA,IACZ,KAAK,MAAM,kBAAkB,MAAM;AAAA;AAEvC,SAAO;AAAA;AAUX,6BAA6B,OAAO,IAAI;AACpC,MAAI,YAAY;AAChB,MAAI,QAAQ,CAAC,CAAC,OAAO;AACrB,MAAI,WAAW;AACf,SAAO,cAAc;AACrB,uBAAqB,WAAU,QAAQ,IAAI;AACvC;AACA,QAAI,aAAa,MAAM,QAAQ;AAE3B,YAAM,OAAO;AAAA;AAEjB,UAAM,KAAK,CAAC,WAAU;AAAA;AAE1B,4BAA0B,IAAI,MAAM,EAAE,WAAW,SAAS;AACtD,UAAM,OAAO;AAAA,MACT;AAAA,MACA;AAAA,MACA,MAAM,eAAe;AAAA;AAEzB,eAAW,YAAY,WAAW;AAC9B,eAAS,IAAI,MAAM;AAAA;AAAA;AAG3B,QAAM,gBAAgB;AAAA,IAElB,UAAU;AAAA,IAEV,OAAO;AAAA,IACP;AAAA,IACA,YAAY,WAAW,KAAK,MAAM;AAAA,IAClC,QAAQ,IAAI,OAAO;AAEf,YAAM,OAAO,YAAY;AACzB,kBAAY,IAAI;AAAA;AAAA,IAEpB,KAAK,IAAI,OAAO;AACZ,kBAAY,IAAI;AAAA;AAAA,IAEpB,OAAO,UAAU;AACb,gBAAU,KAAK;AACf,aAAO,MAAM;AACT,cAAM,QAAQ,UAAU,QAAQ;AAChC,YAAI,QAAQ;AACR,oBAAU,OAAO,OAAO;AAAA;AAAA;AAAA,IAGpC,UAAU;AACN,kBAAY;AACZ,cAAQ,CAAC,CAAC,OAAO;AACjB,iBAAW;AAAA;AAAA,IAEf,GAAG,OAAO,gBAAgB,MAAM;AAC5B,YAAM,OAAO,KAAK;AAClB,YAAM,YAIN,QAAQ,IAAI,oBAAoB,OAAO,oBAAoB;AAC3D,iBAAW,KAAK,IAAI,GAAG,KAAK,IAAI,WAAW,OAAO,MAAM,SAAS;AACjE,UAAI,eAAe;AACf,yBAAiB,KAAK,UAAU,MAAM;AAAA,UAClC;AAAA,UACA;AAAA;AAAA;AAAA;AAAA;AAKhB,SAAO,eAAe,eAAe,YAAY;AAAA,IAC7C,YAAY;AAAA,IACZ,KAAK,MAAM,MAAM,UAAU;AAAA;AAE/B,SAAO,eAAe,eAAe,SAAS;AAAA,IAC1C,YAAY;AAAA,IACZ,KAAK,MAAM,MAAM,UAAU;AAAA;AAE/B,SAAO;AAAA;AA2BX,8BAA8B,MAAM;AAIhC,SAAO,SAAS,OAAO,QAAQ,SAAS,WAAW,SAAS,SAAS;AAErE,MAAI,CAAC,KAAK,SAAS;AACf,YAAQ;AACZ,MAAK,AAA0C,CAAC,KAAK,SAAS,SAAS,CAAC,KAAK,SAAS,MAAM;AACxF,SAAK;AAAA,GAAsC,oBAAoB,KAAK,QAAQ,QAAQ;AAAA;AAExF,SAAO,iBAAiB;AAAA;AAG5B,yBAAyB,OAAO;AAC5B,SAAO,OAAO,UAAU,YAAa,SAAS,OAAO,UAAU;AAAA;AAEnE,qBAAqB,MAAM;AACvB,SAAO,OAAO,SAAS,YAAY,OAAO,SAAS;AAAA;AAGvD,IAAM,0BAA0B,OAAQ,OAAyC,uBAAuB;AAKxG,IAAI;AACJ,AAAC,UAAU,wBAAuB;AAK9B,yBAAsB,uBAAsB,aAAa,KAAK;AAK9D,yBAAsB,uBAAsB,eAAe,KAAK;AAKhE,yBAAsB,uBAAsB,gBAAgB,MAAM;AAAA,GACnE,yBAA0B,yBAAwB;AAErD,IAAM,oBAAoB;AAAA,GACrB,GAAsC,EAAE,qBAAU,mBAAmB;AAClE,WAAO;AAAA,GAAkB,KAAK,UAAU,aAAY,kBAC9C,uBAAuB,KAAK,UAAU,mBACtC;AAAA;AAAA,GAET,GAA8C,EAAE,MAAM,MAAO;AAC1D,WAAO,oBAAoB,KAAK,iBAAiB,eAAe;AAAA;AAAA,GAEnE,GAAuC,EAAE,MAAM,MAAM;AAClD,WAAO,4BAA4B,KAAK,iBAAiB,GAAG;AAAA;AAAA,GAE/D,GAAyC,EAAE,MAAM,MAAM;AACpD,WAAO,8BAA8B,KAAK,iBAAiB,GAAG;AAAA;AAAA,GAEjE,IAA2C,EAAE,MAAM,MAAM;AACtD,WAAO,sDAAsD,KAAK;AAAA;AAAA;AAS1E,2BAA2B,MAAM,QAAQ;AAErC,MAAK,MAAiD;AAClD,WAAO,OAAO,IAAI,MAAM,kBAAkB,MAAM,UAAU;AAAA,MACtD;AAAA,OACC,0BAA0B;AAAA,OAC5B;AAAA,SAEF;AACD,WAAO,OAAO,IAAI,SAAS;AAAA,MACvB;AAAA,OACC,0BAA0B;AAAA,OAC5B;AAAA;AAAA;AAGX,6BAA6B,OAAO,MAAM;AACtC,SAAQ,iBAAiB,SACrB,2BAA2B,SAC1B,SAAQ,QAAQ,CAAC,CAAE,OAAM,OAAO;AAAA;AAEzC,IAAM,kBAAkB,CAAC,UAAU,SAAS;AAC5C,wBAAwB,IAAI;AACxB,MAAI,OAAO,OAAO;AACd,WAAO;AACX,MAAI,GAAG,QAAQ;AACX,WAAO,GAAG;AACd,QAAM,YAAW;AACjB,aAAW,OAAO,iBAAiB;AAC/B,QAAI,OAAO;AACP,gBAAS,OAAO,GAAG;AAAA;AAE3B,SAAO,KAAK,UAAU,WAAU,MAAM;AAAA;AAI1C,IAAM,qBAAqB;AAC3B,IAAM,2BAA2B;AAAA,EAC7B,WAAW;AAAA,EACX,QAAQ;AAAA,EACR,OAAO;AAAA,EACP,KAAK;AAAA;AAGT,IAAM,iBAAiB;AAQvB,wBAAwB,UAAU,cAAc;AAC5C,QAAM,UAAU,OAAO,IAAI,0BAA0B;AAErD,QAAM,QAAQ;AAEd,MAAI,UAAU,QAAQ,QAAQ,MAAM;AAEpC,QAAM,OAAO;AACb,aAAW,WAAW,UAAU;AAE5B,UAAM,gBAAgB,QAAQ,SAAS,KAAK,CAAC;AAE7C,QAAI,QAAQ,UAAU,CAAC,QAAQ;AAC3B,iBAAW;AACf,aAAS,aAAa,GAAG,aAAa,QAAQ,QAAQ,cAAc;AAChE,YAAM,QAAQ,QAAQ;AAEtB,UAAI,kBAAkB,KACjB,SAAQ,YAAY,OAA0C;AACnE,UAAI,MAAM,SAAS,GAA0B;AAEzC,YAAI,CAAC;AACD,qBAAW;AACf,mBAAW,MAAM,MAAM,QAAQ,gBAAgB;AAC/C,2BAAmB;AAAA,iBAEd,MAAM,SAAS,GAAyB;AAC7C,cAAM,EAAE,OAAO,YAAY,UAAU,WAAW;AAChD,aAAK,KAAK;AAAA,UACN,MAAM;AAAA,UACN;AAAA,UACA;AAAA;AAEJ,cAAM,MAAK,SAAS,SAAS;AAE7B,YAAI,QAAO,oBAAoB;AAC3B,6BAAmB;AAEnB,cAAI;AACA,gBAAI,OAAO,IAAI;AAAA,mBAEZ,KAAP;AACI,kBAAM,IAAI,MAAM,oCAAoC,WAAW,WAC3D,IAAI;AAAA;AAAA;AAIhB,YAAI,aAAa,aAAa,OAAO,cAAa,YAAW,IAAI;AAEjE,YAAI,CAAC;AACD,uBAGI,YAAY,QAAQ,SAAS,IACvB,OAAO,gBACP,MAAM;AACpB,YAAI;AACA,wBAAc;AAClB,mBAAW;AACX,2BAAmB;AACnB,YAAI;AACA,6BAAmB;AACvB,YAAI;AACA,6BAAmB;AACvB,YAAI,QAAO;AACP,6BAAmB;AAAA;AAE3B,oBAAc,KAAK;AAAA;AAIvB,UAAM,KAAK;AAAA;AAGf,MAAI,QAAQ,UAAU,QAAQ,KAAK;AAC/B,UAAM,IAAI,MAAM,SAAS;AACzB,UAAM,GAAG,MAAM,GAAG,SAAS,MAAM;AAAA;AAGrC,MAAI,CAAC,QAAQ;AACT,eAAW;AACf,MAAI,QAAQ;AACR,eAAW;AAAA,WAEN,QAAQ,UAAU,CAAC,QAAQ,SAAS;AACzC,eAAW;AACf,QAAM,KAAK,IAAI,OAAO,SAAS,QAAQ,YAAY,KAAK;AACxD,iBAAe,MAAM;AACjB,UAAM,QAAQ,KAAK,MAAM;AACzB,UAAM,SAAS;AACf,QAAI,CAAC;AACD,aAAO;AACX,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,YAAM,QAAQ,MAAM,MAAM;AAC1B,YAAM,MAAM,KAAK,IAAI;AACrB,aAAO,IAAI,QAAQ,SAAS,IAAI,aAAa,MAAM,MAAM,OAAO;AAAA;AAEpE,WAAO;AAAA;AAEX,qBAAmB,QAAQ;AACvB,QAAI,OAAO;AAEX,QAAI,uBAAuB;AAC3B,eAAW,WAAW,UAAU;AAC5B,UAAI,CAAC,wBAAwB,CAAC,KAAK,SAAS;AACxC,gBAAQ;AACZ,6BAAuB;AACvB,iBAAW,SAAS,SAAS;AACzB,YAAI,MAAM,SAAS,GAA0B;AACzC,kBAAQ,MAAM;AAAA,mBAET,MAAM,SAAS,GAAyB;AAC7C,gBAAM,EAAE,OAAO,YAAY,aAAa;AACxC,gBAAM,QAAQ,SAAS,SAAS,OAAO,SAAS;AAChD,cAAI,QAAQ,UAAU,CAAC,YAAY;AAC/B,kBAAM,IAAI,MAAM,mBAAmB;AAAA;AAEvC,gBAAM,OAAO,QAAQ,SACf,MAAM,KAAK,OACX;AACN,cAAI,CAAC,MAAM;AACP,gBAAI,UAAU;AAEV,kBAAI,QAAQ,SAAS,GAAG;AAEpB,oBAAI,KAAK,SAAS;AACd,yBAAO,KAAK,MAAM,GAAG;AAAA;AAGrB,yCAAuB;AAAA;AAAA;AAI/B,oBAAM,IAAI,MAAM,2BAA2B;AAAA;AAEnD,kBAAQ;AAAA;AAAA;AAAA;AAKpB,WAAO,QAAQ;AAAA;AAEnB,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA;AAYR,2BAA2B,GAAG,GAAG;AAC7B,MAAI,IAAI;AACR,SAAO,IAAI,EAAE,UAAU,IAAI,EAAE,QAAQ;AACjC,UAAM,OAAO,EAAE,KAAK,EAAE;AAEtB,QAAI;AACA,aAAO;AACX;AAAA;AAIJ,MAAI,EAAE,SAAS,EAAE,QAAQ;AACrB,WAAO,EAAE,WAAW,KAAK,EAAE,OAAO,KAA4B,KACxD,KACA;AAAA,aAED,EAAE,SAAS,EAAE,QAAQ;AAC1B,WAAO,EAAE,WAAW,KAAK,EAAE,OAAO,KAA4B,KACxD,IACA;AAAA;AAEV,SAAO;AAAA;AASX,gCAAgC,GAAG,GAAG;AAClC,MAAI,IAAI;AACR,QAAM,SAAS,EAAE;AACjB,QAAM,SAAS,EAAE;AACjB,SAAO,IAAI,OAAO,UAAU,IAAI,OAAO,QAAQ;AAC3C,UAAM,OAAO,kBAAkB,OAAO,IAAI,OAAO;AAEjD,QAAI;AACA,aAAO;AACX;AAAA;AAEJ,MAAI,KAAK,IAAI,OAAO,SAAS,OAAO,YAAY,GAAG;AAC/C,QAAI,oBAAoB;AACpB,aAAO;AACX,QAAI,oBAAoB;AACpB,aAAO;AAAA;AAGf,SAAO,OAAO,SAAS,OAAO;AAAA;AAclC,6BAA6B,OAAO;AAChC,QAAM,OAAO,MAAM,MAAM,SAAS;AAClC,SAAO,MAAM,SAAS,KAAK,KAAK,KAAK,SAAS,KAAK;AAAA;AAGvD,IAAM,aAAa;AAAA,EACf,MAAM;AAAA,EACN,OAAO;AAAA;AAEX,IAAM,iBAAiB;AAIvB,sBAAsB,MAAM;AACxB,MAAI,CAAC;AACD,WAAO,CAAC;AACZ,MAAI,SAAS;AACT,WAAO,CAAC,CAAC;AACb,MAAI,CAAC,KAAK,WAAW,MAAM;AACvB,UAAM,IAAI,MAAO,OACX,yCAAyC,qBAAqB,WAC9D,iBAAiB;AAAA;AAG3B,iBAAe,SAAS;AACpB,UAAM,IAAI,MAAM,QAAQ,WAAW,YAAY;AAAA;AAEnD,MAAI,QAAQ;AACZ,MAAI,gBAAgB;AACpB,QAAM,SAAS;AAGf,MAAI;AACJ,6BAA2B;AACvB,QAAI;AACA,aAAO,KAAK;AAChB,cAAU;AAAA;AAGd,MAAI,IAAI;AAER,MAAI;AAEJ,MAAI,SAAS;AAEb,MAAI,WAAW;AACf,2BAAyB;AACrB,QAAI,CAAC;AACD;AACJ,QAAI,UAAU,GAA+B;AACzC,cAAQ,KAAK;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA;AAAA,eAGN,UAAU,KACf,UAAU,KACV,UAAU,GAAuC;AACjD,UAAI,QAAQ,SAAS,KAAM,UAAS,OAAO,SAAS;AAChD,cAAM,uBAAuB;AACjC,cAAQ,KAAK;AAAA,QACT,MAAM;AAAA,QACN,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,YAAY,SAAS,OAAO,SAAS;AAAA,QACrC,UAAU,SAAS,OAAO,SAAS;AAAA;AAAA,WAGtC;AACD,YAAM;AAAA;AAEV,aAAS;AAAA;AAEb,6BAA2B;AACvB,cAAU;AAAA;AAEd,SAAO,IAAI,KAAK,QAAQ;AACpB,WAAO,KAAK;AACZ,QAAI,SAAS,QAAQ,UAAU,GAAoC;AAC/D,sBAAgB;AAChB,cAAQ;AACR;AAAA;AAEJ,YAAQ;AAAA,WACC;AACD,YAAI,SAAS,KAAK;AACd,cAAI,QAAQ;AACR;AAAA;AAEJ;AAAA,mBAEK,SAAS,KAAK;AACnB;AACA,kBAAQ;AAAA,eAEP;AACD;AAAA;AAEJ;AAAA,WACC;AACD;AACA,gBAAQ;AACR;AAAA,WACC;AACD,YAAI,SAAS,KAAK;AACd,kBAAQ;AAAA,mBAEH,eAAe,KAAK,OAAO;AAChC;AAAA,eAEC;AACD;AACA,kBAAQ;AAER,cAAI,SAAS,OAAO,SAAS,OAAO,SAAS;AACzC;AAAA;AAER;AAAA,WACC;AAMD,YAAI,SAAS,KAAK;AAEd,cAAI,SAAS,SAAS,SAAS,MAAM;AACjC,uBAAW,SAAS,MAAM,GAAG,MAAM;AAAA;AAEnC,oBAAQ;AAAA,eAEX;AACD,sBAAY;AAAA;AAEhB;AAAA,WACC;AAED;AACA,gBAAQ;AAER,YAAI,SAAS,OAAO,SAAS,OAAO,SAAS;AACzC;AACJ,mBAAW;AACX;AAAA;AAEA,cAAM;AACN;AAAA;AAAA;AAGZ,MAAI,UAAU;AACV,UAAM,uCAAuC;AACjD;AACA;AAEA,SAAO;AAAA;AAGX,kCAAkC,QAAQ,QAAQ,SAAS;AACvD,QAAM,SAAS,eAAe,aAAa,OAAO,OAAO;AAEzD,MAAK,MAAwC;AACzC,UAAM,eAAe,IAAI;AACzB,eAAW,OAAO,OAAO,MAAM;AAC3B,UAAI,aAAa,IAAI,IAAI;AACrB,aAAK,sCAAsC,IAAI,mBAAmB,OAAO;AAC7E,mBAAa,IAAI,IAAI;AAAA;AAAA;AAG7B,QAAM,UAAU,OAAO,QAAQ;AAAA,IAC3B;AAAA,IACA;AAAA,IAEA,UAAU;AAAA,IACV,OAAO;AAAA;AAEX,MAAI,QAAQ;AAIR,QAAI,CAAC,QAAQ,OAAO,YAAY,CAAC,OAAO,OAAO;AAC3C,aAAO,SAAS,KAAK;AAAA;AAE7B,SAAO;AAAA;AAUX,6BAA6B,QAAQ,eAAe;AAEhD,QAAM,WAAW;AACjB,QAAM,aAAa,IAAI;AACvB,kBAAgB,aAAa,EAAE,QAAQ,OAAO,KAAK,MAAM,WAAW,SAAS;AAC7E,4BAA0B,MAAM;AAC5B,WAAO,WAAW,IAAI;AAAA;AAE1B,oBAAkB,QAAQ,QAAQ,gBAAgB;AAE9C,UAAM,YAAY,CAAC;AACnB,UAAM,uBAAuB,qBAAqB;AAClD,QAAK,MAAwC;AACzC,yCAAmC,sBAAsB;AAAA;AAG7D,yBAAqB,UAAU,kBAAkB,eAAe;AAChE,UAAM,UAAU,aAAa,eAAe;AAE5C,UAAM,oBAAoB,CAAC;AAC3B,QAAI,WAAW,QAAQ;AACnB,YAAM,UAAU,OAAO,OAAO,UAAU,WAAW,CAAC,OAAO,SAAS,OAAO;AAC3E,iBAAW,SAAS,SAAS;AACzB,0BAAkB,KAGlB,qBAAqB,OAAO,IAAI,sBAAsB;AAAA,UAGlD,YAAY,iBACN,eAAe,OAAO,aACtB,qBAAqB;AAAA,UAC3B,MAAM;AAAA,UAEN,SAAS,iBACH,eAAe,SACf;AAAA;AAAA;AAAA;AAMlB,QAAI;AACJ,QAAI;AACJ,eAAW,oBAAoB,mBAAmB;AAC9C,YAAM,EAAE,SAAS;AAIjB,UAAI,UAAU,KAAK,OAAO,KAAK;AAC3B,cAAM,aAAa,OAAO,OAAO;AACjC,cAAM,kBAAkB,WAAW,WAAW,SAAS,OAAO,MAAM,KAAK;AACzE,yBAAiB,OACb,OAAO,OAAO,OAAQ,SAAQ,kBAAkB;AAAA;AAExD,UAA+C,iBAAiB,SAAS,KAAK;AAC1E,cAAM,IAAI,MAAM;AAAA;AAIpB,gBAAU,yBAAyB,kBAAkB,QAAQ;AAC7D,UAAK,AAA0C,UAAU,KAAK,OAAO;AACjE,yCAAiC,SAAS;AAG9C,UAAI,gBAAgB;AAChB,uBAAe,MAAM,KAAK;AAC1B,YAAK,MAAwC;AACzC,0BAAgB,gBAAgB;AAAA;AAAA,aAGnC;AAED,0BAAkB,mBAAmB;AACrC,YAAI,oBAAoB;AACpB,0BAAgB,MAAM,KAAK;AAG/B,YAAI,aAAa,OAAO,QAAQ,CAAC,cAAc,UAAU;AACrD,cAAK,MAAwC;AACzC,oCAAwB,QAAQ;AAAA;AAEpC,sBAAY,OAAO;AAAA;AAAA;AAK3B,UAAI,YAAY,UAAU;AACtB,sBAAc;AAAA;AAElB,UAAI,qBAAqB,UAAU;AAC/B,cAAM,WAAW,qBAAqB;AACtC,iBAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,mBAAS,SAAS,IAAI,SAAS,kBAAkB,eAAe,SAAS;AAAA;AAAA;AAKjF,uBAAiB,kBAAkB;AAAA;AAMvC,WAAO,kBACD,MAAM;AAEJ,kBAAY;AAAA,QAEd;AAAA;AAEV,uBAAqB,YAAY;AAC7B,QAAI,YAAY,aAAa;AACzB,YAAM,UAAU,WAAW,IAAI;AAC/B,UAAI,SAAS;AACT,mBAAW,OAAO;AAClB,iBAAS,OAAO,SAAS,QAAQ,UAAU;AAC3C,gBAAQ,SAAS,QAAQ;AACzB,gBAAQ,MAAM,QAAQ;AAAA;AAAA,WAGzB;AACD,YAAM,QAAQ,SAAS,QAAQ;AAC/B,UAAI,QAAQ,IAAI;AACZ,iBAAS,OAAO,OAAO;AACvB,YAAI,WAAW,OAAO;AAClB,qBAAW,OAAO,WAAW,OAAO;AACxC,mBAAW,SAAS,QAAQ;AAC5B,mBAAW,MAAM,QAAQ;AAAA;AAAA;AAAA;AAIrC,uBAAqB;AACjB,WAAO;AAAA;AAEX,yBAAuB,SAAS;AAC5B,UAAM,QAAQ,mBAAmB,SAAS;AAC1C,aAAS,OAAO,OAAO,GAAG;AAE1B,QAAI,QAAQ,OAAO,QAAQ,CAAC,cAAc;AACtC,iBAAW,IAAI,QAAQ,OAAO,MAAM;AAAA;AAE5C,mBAAiB,WAAU,iBAAiB;AACxC,QAAI;AACJ,QAAI,SAAS;AACb,QAAI;AACJ,QAAI;AACJ,QAAI,UAAU,aAAY,UAAS,MAAM;AACrC,gBAAU,WAAW,IAAI,UAAS;AAClC,UAAI,CAAC;AACD,cAAM,kBAAkB,GAAsC;AAAA,UAC1D;AAAA;AAGR,UAAK,MAAwC;AACzC,cAAM,gBAAgB,OAAO,KAAK,UAAS,UAAU,IAAI,OAAO,eAAa,CAAC,QAAQ,KAAK,KAAK,OAAK,EAAE,SAAS;AAChH,YAAI,cAAc,QAAQ;AACtB,eAAK,+BAA+B,cAAc,KAAK;AAAA;AAAA;AAG/D,aAAO,QAAQ,OAAO;AACtB,eAAS,OAET,mBAAmB,gBAAgB,QAGnC,QAAQ,KACH,OAAO,OAAK,CAAC,EAAE,UACf,OAAO,QAAQ,SAAS,QAAQ,OAAO,KAAK,OAAO,OAAK,EAAE,YAAY,IACtE,IAAI,OAAK,EAAE,QAGhB,UAAS,UACL,mBAAmB,UAAS,QAAQ,QAAQ,KAAK,IAAI,OAAK,EAAE;AAEhE,aAAO,QAAQ,UAAU;AAAA,eAEpB,UAAS,QAAQ,MAAM;AAG5B,aAAO,UAAS;AAChB,UAA+C,CAAC,KAAK,WAAW,MAAM;AAClE,aAAK,2DAA2D,wDAAwD;AAAA;AAE5H,gBAAU,SAAS,KAAK,OAAK,EAAE,GAAG,KAAK;AAEvC,UAAI,SAAS;AAET,iBAAS,QAAQ,MAAM;AACvB,eAAO,QAAQ,OAAO;AAAA;AAAA,WAIzB;AAED,gBAAU,gBAAgB,OACpB,WAAW,IAAI,gBAAgB,QAC/B,SAAS,KAAK,OAAK,EAAE,GAAG,KAAK,gBAAgB;AACnD,UAAI,CAAC;AACD,cAAM,kBAAkB,GAAsC;AAAA,UAC1D;AAAA,UACA;AAAA;AAER,aAAO,QAAQ,OAAO;AAGtB,eAAS,OAAO,IAAI,gBAAgB,QAAQ,UAAS;AACrD,aAAO,QAAQ,UAAU;AAAA;AAE7B,UAAM,UAAU;AAChB,QAAI,gBAAgB;AACpB,WAAO,eAAe;AAElB,cAAQ,QAAQ,cAAc;AAC9B,sBAAgB,cAAc;AAAA;AAElC,WAAO;AAAA,MACH;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM,gBAAgB;AAAA;AAAA;AAI9B,SAAO,QAAQ,WAAS,SAAS;AACjC,yBAAuB;AACnB,aAAS,SAAS;AAClB,eAAW;AAAA;AAEf,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA;AAAA;AAGR,4BAA4B,QAAQ,MAAM;AACtC,QAAM,YAAY;AAClB,aAAW,OAAO,MAAM;AACpB,QAAI,OAAO;AACP,gBAAU,OAAO,OAAO;AAAA;AAEhC,SAAO;AAAA;AAQX,8BAA8B,QAAQ;AAClC,QAAM,aAAa;AAAA,IACf,MAAM,OAAO;AAAA,IACb,UAAU,OAAO;AAAA,IACjB,MAAM,OAAO;AAAA,IACb,MAAM,OAAO,QAAQ;AAAA,IACrB,SAAS,OAAO;AAAA,IAChB,aAAa,OAAO;AAAA,IACpB,OAAO,qBAAqB;AAAA,IAC5B,UAAU,OAAO,YAAY;AAAA,IAC7B,WAAW;AAAA,IACX,aAAa,IAAI;AAAA,IACjB,cAAc,IAAI;AAAA,IAClB,gBAAgB;AAAA,IAGhB,YAAY,gBAAgB,SACtB,OAAO,cAAc,OACrB,OAAO,aAAa,EAAE,SAAS,OAAO;AAAA;AAKhD,SAAO,eAAe,YAAY,QAAQ;AAAA,IACtC,OAAO;AAAA;AAEX,SAAO;AAAA;AAOX,8BAA8B,QAAQ;AAClC,QAAM,cAAc;AAEpB,QAAM,QAAQ,OAAO,SAAS;AAC9B,MAAI,eAAe,QAAQ;AACvB,gBAAY,UAAU;AAAA,SAErB;AAGD,eAAW,QAAQ,OAAO;AACtB,kBAAY,QAAQ,OAAO,UAAU,WAAW,MAAM,QAAQ;AAAA;AAEtE,SAAO;AAAA;AAMX,uBAAuB,QAAQ;AAC3B,SAAO,QAAQ;AACX,QAAI,OAAO,OAAO;AACd,aAAO;AACX,aAAS,OAAO;AAAA;AAEpB,SAAO;AAAA;AAOX,yBAAyB,SAAS;AAC9B,SAAO,QAAQ,OAAO,CAAC,MAAM,WAAW,OAAO,MAAM,OAAO,OAAO;AAAA;AAEvE,sBAAsB,UAAU,gBAAgB;AAC5C,QAAM,UAAU;AAChB,aAAW,OAAO,UAAU;AACxB,YAAQ,OAAO,OAAO,iBAAiB,eAAe,OAAO,SAAS;AAAA;AAE1E,SAAO;AAAA;AAEX,qBAAqB,GAAG,GAAG;AACvB,SAAQ,EAAE,SAAS,EAAE,QACjB,EAAE,aAAa,EAAE,YACjB,EAAE,eAAe,EAAE;AAAA;AAQ3B,yBAAyB,GAAG,GAAG;AAC3B,aAAW,OAAO,EAAE,MAAM;AACtB,QAAI,CAAC,IAAI,YAAY,CAAC,EAAE,KAAK,KAAK,YAAY,KAAK,MAAM;AACrD,aAAO,KAAK,UAAU,EAAE,OAAO,mCAAmC,EAAE,OAAO,+CAA+C,IAAI;AAAA;AAEtI,aAAW,OAAO,EAAE,MAAM;AACtB,QAAI,CAAC,IAAI,YAAY,CAAC,EAAE,KAAK,KAAK,YAAY,KAAK,MAAM;AACrD,aAAO,KAAK,UAAU,EAAE,OAAO,mCAAmC,EAAE,OAAO,+CAA+C,IAAI;AAAA;AAAA;AAS1I,4CAA4C,sBAAsB,QAAQ;AACtE,MAAI,UACA,OAAO,OAAO,QACd,CAAC,qBAAqB,QACtB,CAAC,qBAAqB,MAAM;AAC5B,SAAK,oBAAoB,OAAO,OAAO,OAAO;AAAA;AAAA;AAGtD,iCAAiC,QAAQ,QAAQ;AAC7C,WAAS,WAAW,QAAQ,UAAU,WAAW,SAAS,QAAQ;AAC9D,QAAI,SAAS,OAAO,SAAS,OAAO,MAAM;AACtC,YAAM,IAAI,MAAM,kBAAkB,OAAO,OAAO,8BAA8B,WAAW,WAAW,UAAU;AAAA;AAAA;AAAA;AAI1H,0CAA0C,QAAQ,QAAQ;AACtD,aAAW,OAAO,OAAO,MAAM;AAC3B,QAAI,CAAC,OAAO,KAAK,KAAK,YAAY,KAAK,MAAM;AACzC,aAAO,KAAK,kBAAkB,OAAO,OAAO,+CAA+C,IAAI,wBAAwB,OAAO,OAAO;AAAA;AAAA;AAYjJ,4BAA4B,SAAS,UAAU;AAE3C,MAAI,QAAQ;AACZ,MAAI,QAAQ,SAAS;AACrB,SAAO,UAAU,OAAO;AACpB,UAAM,MAAO,QAAQ,SAAU;AAC/B,UAAM,YAAY,uBAAuB,SAAS,SAAS;AAC3D,QAAI,YAAY,GAAG;AACf,cAAQ;AAAA,WAEP;AACD,cAAQ,MAAM;AAAA;AAAA;AAItB,QAAM,oBAAoB,qBAAqB;AAC/C,MAAI,mBAAmB;AACnB,YAAQ,SAAS,YAAY,mBAAmB,QAAQ;AACxD,QAA+C,QAAQ,GAAG;AAEtD,WAAK,2BAA2B,kBAAkB,OAAO,qBAAqB,QAAQ,OAAO;AAAA;AAAA;AAGrG,SAAO;AAAA;AAEX,8BAA8B,SAAS;AACnC,MAAI,WAAW;AACf,SAAQ,WAAW,SAAS,QAAS;AACjC,QAAI,YAAY,aACZ,uBAAuB,SAAS,cAAc,GAAG;AACjD,aAAO;AAAA;AAAA;AAGf;AAAA;AASJ,qBAAqB,EAAE,UAAU;AAC7B,SAAO,CAAC,CAAE,QAAO,QACZ,OAAO,cAAc,OAAO,KAAK,OAAO,YAAY,UACrD,OAAO;AAAA;AAYf,oBAAoB,QAAQ;AACxB,QAAM,QAAQ;AAGd,MAAI,WAAW,MAAM,WAAW;AAC5B,WAAO;AACX,QAAM,eAAe,OAAO,OAAO;AACnC,QAAM,eAAgB,gBAAe,OAAO,MAAM,KAAK,QAAQ,MAAM;AACrE,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,EAAE,GAAG;AAE1C,UAAM,cAAc,aAAa,GAAG,QAAQ,SAAS;AAErD,UAAM,QAAQ,YAAY,QAAQ;AAClC,UAAM,MAAM,OAAO,QAAQ,IAAI,cAAc,YAAY,MAAM,GAAG;AAClE,UAAM,QAAQ,QAAQ,IAAI,OAAO,OAAO,YAAY,MAAM,QAAQ;AAClE,QAAI,OAAO,OAAO;AAEd,UAAI,eAAe,MAAM;AACzB,UAAI,CAAC,QAAQ,eAAe;AACxB,uBAAe,MAAM,OAAO,CAAC;AAAA;AAEjC,mBAAa,KAAK;AAAA,WAEjB;AACD,YAAM,OAAO;AAAA;AAAA;AAGrB,SAAO;AAAA;AAWX,wBAAwB,OAAO;AAC3B,MAAI,SAAS;AACb,WAAS,OAAO,OAAO;AACnB,UAAM,QAAQ,MAAM;AACpB,UAAM,eAAe;AACrB,QAAI,SAAS,MAAM;AAEf,UAAI,UAAU,QAAW;AACrB,kBAAW,QAAO,SAAS,MAAM,MAAM;AAAA;AAE3C;AAAA;AAGJ,UAAM,SAAS,QAAQ,SACjB,MAAM,IAAI,OAAK,KAAK,iBAAiB,MACrC,CAAC,SAAS,iBAAiB;AACjC,WAAO,QAAQ,YAAS;AAGpB,UAAI,WAAU,QAAW;AAErB,kBAAW,QAAO,SAAS,MAAM,MAAM;AACvC,YAAI,UAAS;AACT,oBAAU,MAAM;AAAA;AAAA;AAAA;AAIhC,SAAO;AAAA;AAUX,wBAAwB,OAAO;AAC3B,QAAM,kBAAkB;AACxB,aAAW,OAAO,OAAO;AACrB,UAAM,QAAQ,MAAM;AACpB,QAAI,UAAU,QAAW;AACrB,sBAAgB,OAAO,QAAQ,SACzB,MAAM,IAAI,OAAM,KAAK,OAAO,OAAO,KAAK,KACxC,SAAS,OACL,QACA,KAAK;AAAA;AAAA;AAGvB,SAAO;AAAA;AAUX,IAAM,kBAAkB,OAAQ,OAAyC,iCAAiC;AAO1G,IAAM,eAAe,OAAQ,OAAyC,sBAAsB;AAO5F,IAAM,YAAY,OAAQ,OAAyC,WAAW;AAO9E,IAAM,mBAAmB,OAAQ,OAAyC,mBAAmB;AAO7F,IAAM,wBAAwB,OAAQ,OAAyC,yBAAyB;AAKxG,wBAAwB;AACpB,MAAI,WAAW;AACf,eAAa,SAAS;AAClB,aAAS,KAAK;AACd,WAAO,MAAM;AACT,YAAM,IAAI,SAAS,QAAQ;AAC3B,UAAI,IAAI;AACJ,iBAAS,OAAO,GAAG;AAAA;AAAA;AAG/B,mBAAiB;AACb,eAAW;AAAA;AAEf,SAAO;AAAA,IACH;AAAA,IACA,MAAM,MAAM,SAAS;AAAA,IACrB;AAAA;AAAA;AAIR,uBAAuB,QAAQ,MAAM,OAAO;AACxC,QAAM,iBAAiB,MAAM;AACzB,WAAO,MAAM,OAAO;AAAA;AAExB,cAAY;AACZ,gBAAc;AACd,cAAY,MAAM;AACd,WAAO,MAAM,IAAI;AAAA;AAErB,SAAO,MAAM,IAAI;AAAA;AASrB,4BAA4B,YAAY;AACpC,MAA+C,CAAC,sBAAsB;AAClE,SAAK;AACL;AAAA;AAEJ,QAAM,eAAe,OAAO,iBAE5B,IAAI;AACJ,MAAI,CAAC,cAAc;AACf,IACI,KAAK;AACT;AAAA;AAEJ,gBAAc,cAAc,eAAe;AAAA;AAS/C,6BAA6B,aAAa;AACtC,MAA+C,CAAC,sBAAsB;AAClE,SAAK;AACL;AAAA;AAEJ,QAAM,eAAe,OAAO,iBAE5B,IAAI;AACJ,MAAI,CAAC,cAAc;AACf,IACI,KAAK;AACT;AAAA;AAEJ,gBAAc,cAAc,gBAAgB;AAAA;AAEhD,0BAA0B,OAAO,IAAI,MAAM,QAAQ,MAAM,iBAAiB,QAAM,MAAM;AAElF,QAAM,qBAAqB,UAEtB,QAAO,eAAe,QAAQ,OAAO,eAAe,SAAS;AAClE,SAAO,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC1C,UAAM,OAAO,CAAC,UAAU;AACpB,UAAI,UAAU,OAAO;AACjB,eAAO,kBAAkB,GAAuC;AAAA,UAC5D;AAAA,UACA;AAAA;AAAA,iBAGC,iBAAiB,OAAO;AAC7B,eAAO;AAAA,iBAEF,gBAAgB,QAAQ;AAC7B,eAAO,kBAAkB,GAA8C;AAAA,UACnE,MAAM;AAAA,UACN,IAAI;AAAA;AAAA,aAGP;AACD,YAAI,sBAEA,OAAO,eAAe,UAAU,sBAChC,OAAO,UAAU,YAAY;AAC7B,6BAAmB,KAAK;AAAA;AAE5B;AAAA;AAAA;AAIR,UAAM,cAAc,eAAe,MAAM,MAAM,KAAK,UAAU,OAAO,UAAU,OAAO,IAAI,MAAO,OAAyC,oBAAoB,MAAM,IAAI,QAAQ;AAChL,QAAI,YAAY,QAAQ,QAAQ;AAChC,QAAI,MAAM,SAAS;AACf,kBAAY,UAAU,KAAK;AAC/B,QAA+C,MAAM,SAAS,GAAG;AAC7D,YAAM,UAAU,kDAAkD,MAAM,OAAO,MAAM,MAAM,OAAO,MAAM;AAAA,EAAQ,MAAM;AAAA;AACtH,UAAI,OAAO,gBAAgB,YAAY,UAAU,aAAa;AAC1D,oBAAY,UAAU,KAAK,mBAAiB;AAExC,cAAI,CAAC,KAAK,SAAS;AACf,iBAAK;AACL,mBAAO,QAAQ,OAAO,IAAI,MAAM;AAAA;AAEpC,iBAAO;AAAA;AAAA,iBAGN,gBAAgB,QAAW;AAEhC,YAAI,CAAC,KAAK,SAAS;AACf,eAAK;AACL,iBAAO,IAAI,MAAM;AACjB;AAAA;AAAA;AAAA;AAIZ,cAAU,MAAM,SAAO,OAAO;AAAA;AAAA;AAGtC,6BAA6B,MAAM,IAAI,MAAM;AACzC,MAAI,SAAS;AACb,SAAO,WAAY;AACf,QAAI,aAAa;AACb,WAAK,0FAA0F,KAAK,iBAAiB,GAAG;AAE5H,SAAK,UAAU;AACf,QAAI,WAAW;AACX,WAAK,MAAM,MAAM;AAAA;AAAA;AAG7B,iCAAiC,SAAS,WAAW,IAAI,MAAM,iBAAiB,QAAM,MAAM;AACxF,QAAM,SAAS;AACf,aAAW,UAAU,SAAS;AAC1B,QAAK,AAA0C,CAAC,OAAO,cAAc,CAAC,OAAO,SAAS,QAAQ;AAC1F,WAAK,qBAAqB,OAAO;AAAA;AAGrC,eAAW,QAAQ,OAAO,YAAY;AAClC,UAAI,eAAe,OAAO,WAAW;AACrC,UAAK,MAAwC;AACzC,YAAI,CAAC,gBACA,OAAO,iBAAiB,YACrB,OAAO,iBAAiB,YAAa;AACzC,eAAK,cAAc,8BAA8B,OAAO,6CACnB,OAAO;AAG5C,gBAAM,IAAI,MAAM;AAAA,mBAEX,UAAU,cAAc;AAG7B,eAAK,cAAc,8BAA8B,OAAO;AAKxD,gBAAM,UAAU;AAChB,yBAAe,MAAM;AAAA,mBAEhB,aAAa,iBAElB,CAAC,aAAa,qBAAqB;AACnC,uBAAa,sBAAsB;AACnC,eAAK,cAAc,8BAA8B,OAAO;AAAA;AAAA;AAOhE,UAAI,cAAc,sBAAsB,CAAC,OAAO,UAAU;AACtD;AACJ,UAAI,iBAAiB,eAAe;AAEhC,cAAM,UAAU,aAAa,aAAa;AAC1C,cAAM,QAAQ,QAAQ;AACtB,iBACI,OAAO,KAAK,iBAAiB,OAAO,IAAI,MAAM,QAAQ,MAAM;AAAA,aAE/D;AAED,YAAI,mBAAmB;AACvB,YAA+C,CAAE,YAAW,mBAAmB;AAC3E,eAAK,cAAc,8BAA8B,OAAO;AACxD,6BAAmB,QAAQ,QAAQ;AAAA;AAEvC,eAAO,KAAK,MAAM,iBAAiB,KAAK,cAAY;AAChD,cAAI,CAAC;AACD,kBAAM,IAAI,MAAM,+BAA+B,aAAa,OAAO;AACvE,gBAAM,oBAAoB,WAAW,YAC/B,SAAS,UACT;AAEN,iBAAO,KAAK,QAAQ;AAGpB,iBAAO,WAAW,QAAQ;AAE1B,gBAAM,UAAU,kBAAkB,aAAa;AAC/C,gBAAM,QAAQ,QAAQ;AACtB,iBAAQ,SACJ,iBAAiB,OAAO,IAAI,MAAM,QAAQ,MAAM;AAAA;AAAA;AAAA;AAAA;AAKpE,SAAO;AAAA;AAOX,2BAA2B,OAAO;AAC9B,SAAO,MAAM,QAAQ,MAAM,YAAU,OAAO,YACtC,QAAQ,OAAO,IAAI,MAAM,0CACzB,QAAQ,IAAI,MAAM,QAAQ,IAAI,YAAU,OAAO,cAC7C,QAAQ,IAAI,OAAO,KAAK,OAAO,YAAY,OAAO,CAAC,UAAU,SAAS;AAClE,UAAM,eAAe,OAAO,WAAW;AACvC,QAAI,OAAO,iBAAiB,cACxB,CAAE,kBAAiB,eAAe;AAClC,eAAS,KAAK,eAAe,KAAK,cAAY;AAC1C,YAAI,CAAC;AACD,iBAAO,QAAQ,OAAO,IAAI,MAAM,+BAA+B,aAAa,OAAO;AACvF,cAAM,oBAAoB,WAAW,YAC/B,SAAS,UACT;AAEN,eAAO,KAAK,QAAQ;AAGpB,eAAO,WAAW,QAAQ;AAC1B;AAAA;AAAA;AAGR,WAAO;AAAA,KACR,OAAO,KAAK,MAAM;AAAA;AAUjC,iBAAiB,OAAO;AACpB,QAAM,SAAS,OAAO;AACtB,QAAM,eAAe,OAAO;AAC5B,MAAI,cAAc;AAClB,MAAI,aAAa;AACjB,QAAM,QAAQ,SAAS,MAAM;AACzB,UAAM,KAAK,MAAM,MAAM;AACvB,QAAgD,CAAC,eAAe,OAAO,YAAa;AAChF,UAAI,CAAC,gBAAgB,KAAK;AACtB,YAAI,aAAa;AACb,eAAK;AAAA,QAAmD,IAAI;AAAA,iBAAoB,YAAY;AAAA,WAAc;AAAA,eAEzG;AACD,eAAK;AAAA,QAAmD,IAAI;AAAA,WAAc;AAAA;AAAA;AAGlF,mBAAa;AACb,oBAAc;AAAA;AAElB,WAAO,OAAO,QAAQ;AAAA;AAE1B,QAAM,oBAAoB,SAAS,MAAM;AACrC,UAAM,EAAE,YAAY,MAAM;AAC1B,UAAM,EAAE,WAAW;AACnB,UAAM,eAAe,QAAQ,SAAS;AACtC,UAAM,iBAAiB,aAAa;AACpC,QAAI,CAAC,gBAAgB,CAAC,eAAe;AACjC,aAAO;AACX,UAAM,QAAQ,eAAe,UAAU,kBAAkB,KAAK,MAAM;AACpE,QAAI,QAAQ;AACR,aAAO;AAEX,UAAM,mBAAmB,gBAAgB,QAAQ,SAAS;AAC1D,WAEA,SAAS,KAIL,gBAAgB,kBAAkB,oBAElC,eAAe,eAAe,SAAS,GAAG,SAAS,mBACjD,eAAe,UAAU,kBAAkB,KAAK,MAAM,QAAQ,SAAS,OACvE;AAAA;AAEV,QAAM,WAAW,SAAS,MAAM,kBAAkB,QAAQ,MACtD,eAAe,aAAa,QAAQ,MAAM,MAAM;AACpD,QAAM,gBAAgB,SAAS,MAAM,kBAAkB,QAAQ,MAC3D,kBAAkB,UAAU,aAAa,QAAQ,SAAS,KAC1D,0BAA0B,aAAa,QAAQ,MAAM,MAAM;AAC/D,oBAAkB,IAAI,IAAI;AACtB,QAAI,WAAW,IAAI;AACf,YAAM,IAAI,OAAO,MAAM,MAAM,WAAW,YAAY,QAAQ,MAAM,MAAM,KAEtE,MAAM;AACR,UAAI,MAAM,kBACN,OAAO,aAAa,eACpB,yBAAyB,UAAU;AACnC,iBAAS,oBAAoB,MAAM;AAAA;AAEvC,aAAO;AAAA;AAEX,WAAO,QAAQ;AAAA;AAGnB,MAA0E,WAAW;AACjF,UAAM,WAAW;AACjB,QAAI,UAAU;AACV,YAAM,sBAAsB;AAAA,QACxB,OAAO,MAAM;AAAA,QACb,UAAU,SAAS;AAAA,QACnB,eAAe,cAAc;AAAA,QAC7B,OAAO;AAAA;AAGX,eAAS,iBAAiB,SAAS,kBAAkB;AAErD,eAAS,eAAe,KAAK;AAC7B,kBAAY,MAAM;AACd,4BAAoB,QAAQ,MAAM;AAClC,4BAAoB,WAAW,SAAS;AACxC,4BAAoB,gBAAgB,cAAc;AAClD,4BAAoB,QAAQ,gBAAgB,MAAM,MAAM,OAClD,OACA;AAAA,SACP,EAAE,OAAO;AAAA;AAAA;AAMpB,SAAO;AAAA,IACH;AAAA,IACA,MAAM,SAAS,MAAM,MAAM,MAAM;AAAA,IACjC;AAAA,IACA;AAAA,IACA;AAAA;AAAA;AAGR,2BAA2B,QAAQ;AAC/B,SAAO,OAAO,WAAW,IAAI,OAAO,KAAK;AAAA;AAE7C,IAAM,iBAA+B,gBAAgB;AAAA,EACjD,MAAM;AAAA,EACN,cAAc,EAAE,MAAM;AAAA,EACtB,OAAO;AAAA,IACH,IAAI;AAAA,MACA,MAAM,CAAC,QAAQ;AAAA,MACf,UAAU;AAAA;AAAA,IAEd,SAAS;AAAA,IACT,aAAa;AAAA,IAEb,kBAAkB;AAAA,IAClB,QAAQ;AAAA,IACR,kBAAkB;AAAA,MACd,MAAM;AAAA,MACN,SAAS;AAAA;AAAA,IAEb,gBAAgB;AAAA;AAAA,EAEpB;AAAA,EACA,MAAM,OAAO,EAAE,SAAS;AACpB,UAAM,OAAO,SAAS,QAAQ;AAC9B,UAAM,EAAE,YAAY,OAAO;AAC3B,UAAM,UAAU,SAAS,MAAO;AAAA,OAC3B,aAAa,MAAM,aAAa,QAAQ,iBAAiB,wBAAwB,KAAK;AAAA,OAMtF,aAAa,MAAM,kBAAkB,QAAQ,sBAAsB,8BAA8B,KAAK;AAAA;AAE3G,WAAO,MAAM;AACT,YAAM,WAAW,MAAM,WAAW,kBAAkB,MAAM,QAAQ;AAClE,aAAO,MAAM,SACP,WACA,EAAE,KAAK;AAAA,QACL,gBAAgB,KAAK,gBACf,MAAM,mBACN;AAAA,QACN,MAAM,KAAK;AAAA,QAGX,SAAS,KAAK;AAAA,QACd,OAAO,QAAQ;AAAA,SAChB;AAAA;AAAA;AAAA;AASnB,IAAM,aAAa;AACnB,oBAAoB,GAAG;AAEnB,MAAI,EAAE,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE;AACxC;AAEJ,MAAI,EAAE;AACF;AAEJ,MAAI,EAAE,WAAW,UAAa,EAAE,WAAW;AACvC;AAGJ,MAAI,EAAE,iBAAiB,EAAE,cAAc,cAAc;AAEjD,UAAM,SAAS,EAAE,cAAc,aAAa;AAC5C,QAAI,cAAc,KAAK;AACnB;AAAA;AAGR,MAAI,EAAE;AACF,MAAE;AACN,SAAO;AAAA;AAEX,wBAAwB,OAAO,OAAO;AAClC,aAAW,OAAO,OAAO;AACrB,UAAM,aAAa,MAAM;AACzB,UAAM,aAAa,MAAM;AACzB,QAAI,OAAO,eAAe,UAAU;AAChC,UAAI,eAAe;AACf,eAAO;AAAA,WAEV;AACD,UAAI,CAAC,QAAQ,eACT,WAAW,WAAW,WAAW,UACjC,WAAW,KAAK,CAAC,OAAO,MAAM,UAAU,WAAW;AACnD,eAAO;AAAA;AAAA;AAGnB,SAAO;AAAA;AAMX,yBAAyB,QAAQ;AAC7B,SAAO,SAAU,OAAO,UAAU,OAAO,QAAQ,OAAO,OAAO,OAAQ;AAAA;AAQ3E,IAAM,eAAe,CAAC,WAAW,aAAa,iBAAiB,aAAa,OACtE,YACA,eAAe,OACX,cACA;AAEV,IAAM,iBAA+B,gBAAgB;AAAA,EACjD,MAAM;AAAA,EAEN,cAAc;AAAA,EACd,OAAO;AAAA,IACH,MAAM;AAAA,MACF,MAAM;AAAA,MACN,SAAS;AAAA;AAAA,IAEb,OAAO;AAAA;AAAA,EAIX,cAAc,EAAE,MAAM;AAAA,EACtB,MAAM,OAAO,EAAE,OAAO,SAAS;AAC3B,IAA2C;AAC3C,UAAM,gBAAgB,OAAO;AAC7B,UAAM,iBAAiB,SAAS,MAAM,MAAM,SAAS,cAAc;AACnE,UAAM,gBAAgB,OAAO,cAAc;AAG3C,UAAM,QAAQ,SAAS,MAAM;AACzB,UAAI,eAAe,MAAM;AACzB,YAAM,EAAE,YAAY,eAAe;AACnC,UAAI;AACJ,aAAQ,gBAAe,QAAQ,kBAC3B,CAAC,aAAa,YAAY;AAC1B;AAAA;AAEJ,aAAO;AAAA;AAEX,UAAM,kBAAkB,SAAS,MAAM,eAAe,MAAM,QAAQ,MAAM;AAC1E,YAAQ,cAAc,SAAS,MAAM,MAAM,QAAQ;AACnD,YAAQ,iBAAiB;AACzB,YAAQ,uBAAuB;AAC/B,UAAM,UAAU;AAGhB,UAAM,MAAM,CAAC,QAAQ,OAAO,gBAAgB,OAAO,MAAM,OAAO,CAAC,CAAC,UAAU,IAAI,OAAO,CAAC,aAAa,MAAM,aAAa;AAEpH,UAAI,IAAI;AAGJ,WAAG,UAAU,QAAQ;AAOrB,YAAI,QAAQ,SAAS,MAAM,YAAY,aAAa,aAAa;AAC7D,cAAI,CAAC,GAAG,YAAY,MAAM;AACtB,eAAG,cAAc,KAAK;AAAA;AAE1B,cAAI,CAAC,GAAG,aAAa,MAAM;AACvB,eAAG,eAAe,KAAK;AAAA;AAAA;AAAA;AAKnC,UAAI,YACA,MAGC,EAAC,QAAQ,CAAC,kBAAkB,IAAI,SAAS,CAAC,cAAc;AACzD,QAAC,IAAG,eAAe,SAAS,IAAI,QAAQ,cAAY,SAAS;AAAA;AAAA,OAElE,EAAE,OAAO;AACZ,WAAO,MAAM;AACT,YAAM,QAAQ,eAAe;AAG7B,YAAM,cAAc,MAAM;AAC1B,YAAM,eAAe,gBAAgB;AACrC,YAAM,gBAAgB,gBAAgB,aAAa,WAAW;AAC9D,UAAI,CAAC,eAAe;AAChB,eAAO,cAAc,MAAM,SAAS,EAAE,WAAW,eAAe;AAAA;AAGpE,YAAM,mBAAmB,aAAa,MAAM;AAC5C,YAAM,aAAa,mBACb,qBAAqB,OACjB,MAAM,SACN,OAAO,qBAAqB,aACxB,iBAAiB,SACjB,mBACR;AACN,YAAM,mBAAmB,WAAS;AAE9B,YAAI,MAAM,UAAU,aAAa;AAC7B,uBAAa,UAAU,eAAe;AAAA;AAAA;AAG9C,YAAM,YAAY,EAAE,eAAe,OAAO,IAAI,YAAY,OAAO;AAAA,QAC7D;AAAA,QACA,KAAK;AAAA;AAET,UAAM,AACF,aACA,UAAU,KAAK;AAEf,cAAM,OAAO;AAAA,UACT,OAAO,MAAM;AAAA,UACb,MAAM,aAAa;AAAA,UACnB,MAAM,aAAa;AAAA,UACnB,MAAM,aAAa;AAAA;AAEvB,cAAM,oBAAoB,QAAQ,UAAU,OACtC,UAAU,IAAI,IAAI,OAAK,EAAE,KACzB,CAAC,UAAU,IAAI;AACrB,0BAAkB,QAAQ,cAAY;AAElC,mBAAS,iBAAiB;AAAA;AAAA;AAGlC,aAGA,cAAc,MAAM,SAAS,EAAE,WAAW,WAAW,YACjD;AAAA;AAAA;AAAA;AAIhB,uBAAuB,MAAM,MAAM;AAC/B,MAAI,CAAC;AACD,WAAO;AACX,QAAM,cAAc,KAAK;AACzB,SAAO,YAAY,WAAW,IAAI,YAAY,KAAK;AAAA;AAOvD,IAAM,aAAa;AAGnB,+BAA+B;AAC3B,QAAM,WAAW;AACjB,QAAM,aAAa,SAAS,UAAU,SAAS,OAAO,KAAK;AAC3D,QAAM,oBAAoB,SAAS,UAAU,SAAS,OAAO,WAAW,SAAS,OAAO,QAAQ;AAChG,MAAI,cACC,gBAAe,eAAe,WAAW,SAAS,kBACnD,OAAO,sBAAsB,YAC7B,kBAAkB,SAAS,cAAc;AACzC,UAAM,OAAO,eAAe,cAAc,eAAe;AACzD,SAAK;AAAA;AAAA;AAAA;AAAA,KAGK;AAAA;AAAA,MAEC;AAAA;AAAA;AAAA;AAYnB,6BAA6B,eAAe,SAAS;AACjD,QAAM,OAAO,OAAO,IAAI,eAAe;AAAA,IAEnC,SAAS,cAAc,QAAQ,IAAI,aAAW,KAAK,SAAS,CAAC,aAAa,YAAY;AAAA;AAE1F,SAAO;AAAA,IACH,SAAS;AAAA,MACL,MAAM;AAAA,MACN,UAAU;AAAA,MACV,SAAS,cAAc;AAAA,MACvB;AAAA,MACA,OAAO;AAAA;AAAA;AAAA;AAInB,uBAAuB,SAAS;AAC5B,SAAO;AAAA,IACH,SAAS;AAAA,MACL;AAAA;AAAA;AAAA;AAKZ,IAAI,WAAW;AACf,qBAAqB,KAAK,QAAQ,SAAS;AAGvC,MAAI,OAAO;AACP;AACJ,SAAO,gBAAgB;AAEvB,QAAM,KAAK;AACX,sBAAoB;AAAA,IAChB,IAAI,qBAAsB,MAAK,MAAM,KAAK;AAAA,IAC1C,OAAO;AAAA,IACP,aAAa;AAAA,IACb,UAAU;AAAA,IACV,MAAM;AAAA,IACN,qBAAqB,CAAC;AAAA,IACtB;AAAA,KACD,SAAO;AACN,QAAI,OAAO,IAAI,QAAQ,YAAY;AAC/B,cAAQ,KAAK;AAAA;AAGjB,QAAI,GAAG,iBAAiB,CAAC,SAAS,QAAQ;AACtC,UAAI,QAAQ,cAAc;AACtB,gBAAQ,aAAa,MAAM,KAAK;AAAA,UAC5B,MAAM;AAAA,UACN,KAAK;AAAA,UACL,UAAU;AAAA,UACV,OAAO,oBAAoB,OAAO,aAAa,OAAO;AAAA;AAAA;AAAA;AAKlE,QAAI,GAAG,mBAAmB,CAAC,EAAE,UAAU,MAAM,wBAAwB;AACjE,UAAI,kBAAkB,gBAAgB;AAClC,cAAM,OAAO,kBAAkB;AAC/B,aAAK,KAAK,KAAK;AAAA,UACX,OAAQ,MAAK,OAAO,GAAG,KAAK,KAAK,iBAAiB,MAAM,KAAK;AAAA,UAC7D,WAAW;AAAA,UACX,SAAS;AAAA,UACT,iBAAiB;AAAA;AAAA;AAIzB,UAAI,QAAQ,kBAAkB,iBAAiB;AAC3C,0BAAkB,gBAAgB;AAClC,0BAAkB,eAAe,QAAQ,kBAAgB;AACrD,cAAI,QAAQ,aAAa,MAAM;AAC/B,cAAI,kBAAkB;AACtB,cAAI,UAAU;AACd,cAAI,YAAY;AAChB,cAAI,aAAa,OAAO;AACpB,oBAAQ,aAAa;AACrB,8BAAkB;AAClB,wBAAY;AAAA,qBAEP,aAAa,eAAe;AACjC,8BAAkB;AAClB,sBAAU;AAAA,qBAEL,aAAa,UAAU;AAC5B,8BAAkB;AAClB,sBAAU;AAAA;AAEd,eAAK,KAAK,KAAK;AAAA,YACX;AAAA,YACA;AAAA,YACA;AAAA,YACA;AAAA;AAAA;AAAA;AAAA;AAKhB,UAAM,OAAO,cAAc,MAAM;AAE7B;AACA,UAAI;AACJ,UAAI,kBAAkB;AACtB,UAAI,mBAAmB;AAAA;AAE3B,UAAM,qBAAqB,wBAAwB;AACnD,QAAI,iBAAiB;AAAA,MACjB,IAAI;AAAA,MACJ,OAAO,SAAS,KAAK,MAAM,KAAK;AAAA,MAChC,OAAO;AAAA;AAQX,WAAO,QAAQ,CAAC,OAAO,OAAO;AAC1B,UAAI,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,OAAO;AAAA,UACH,OAAO;AAAA,UACP,UAAU,GAAG;AAAA,UACb,SAAS;AAAA,UACT,MAAM,IAAI;AAAA,UACV,MAAM,EAAE;AAAA,UACR,SAAS,GAAG,KAAK;AAAA;AAAA;AAAA;AAK7B,QAAI,eAAe;AACnB,WAAO,WAAW,CAAC,IAAI,SAAS;AAC5B,YAAM,OAAO;AAAA,QACT,OAAO,cAAc;AAAA,QACrB,MAAM,oBAAoB,MAAM;AAAA,QAChC,IAAI,oBAAoB,IAAI;AAAA;AAGhC,aAAO,eAAe,GAAG,MAAM,kBAAkB;AAAA,QAC7C,OAAO;AAAA;AAEX,UAAI,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,OAAO;AAAA,UACH,MAAM,IAAI;AAAA,UACV,OAAO;AAAA,UACP,UAAU,GAAG;AAAA,UACb;AAAA,UACA,SAAS,GAAG,KAAK;AAAA;AAAA;AAAA;AAI7B,WAAO,UAAU,CAAC,IAAI,MAAM,YAAY;AACpC,YAAM,OAAO;AAAA,QACT,OAAO,cAAc;AAAA;AAEzB,UAAI,SAAS;AACT,aAAK,UAAU;AAAA,UACX,SAAS;AAAA,YACL,MAAM;AAAA,YACN,UAAU;AAAA,YACV,SAAS,UAAU,QAAQ,UAAU;AAAA,YACrC,SAAS;AAAA,YACT,OAAO;AAAA;AAAA;AAGf,aAAK,SAAS,cAAc;AAAA,aAE3B;AACD,aAAK,SAAS,cAAc;AAAA;AAGhC,WAAK,OAAO,oBAAoB,MAAM;AACtC,WAAK,KAAK,oBAAoB,IAAI;AAClC,UAAI,iBAAiB;AAAA,QACjB,SAAS;AAAA,QACT,OAAO;AAAA,UACH,OAAO;AAAA,UACP,UAAU,GAAG;AAAA,UACb,MAAM,IAAI;AAAA,UACV;AAAA,UACA,SAAS,UAAU,YAAY;AAAA,UAC/B,SAAS,GAAG,KAAK;AAAA;AAAA;AAAA;AAO7B,UAAM,oBAAoB,sBAAsB;AAChD,QAAI,aAAa;AAAA,MACb,IAAI;AAAA,MACJ,OAAO,WAAY,MAAK,MAAM,KAAK;AAAA,MACnC,MAAM;AAAA,MACN,uBAAuB;AAAA;AAE3B,iCAA6B;AAEzB,UAAI,CAAC;AACD;AACJ,YAAM,UAAU;AAEhB,UAAI,SAAS,QAAQ,YAAY,OAAO,WAAS,CAAC,MAAM,UAGpD,CAAC,MAAM,OAAO,OAAO;AAEzB,aAAO,QAAQ;AAEf,UAAI,QAAQ,QAAQ;AAChB,iBAAS,OAAO,OAAO,WAEvB,gBAAgB,OAAO,QAAQ,OAAO;AAAA;AAG1C,aAAO,QAAQ,WAAS,sBAAsB,OAAO,OAAO,aAAa;AACzE,cAAQ,YAAY,OAAO,IAAI;AAAA;AAEnC,QAAI;AACJ,QAAI,GAAG,iBAAiB,aAAW;AAC/B,4BAAsB;AACtB,UAAI,QAAQ,QAAQ,OAAO,QAAQ,gBAAgB,mBAAmB;AAClE;AAAA;AAAA;AAMR,QAAI,GAAG,kBAAkB,aAAW;AAChC,UAAI,QAAQ,QAAQ,OAAO,QAAQ,gBAAgB,mBAAmB;AAClE,cAAM,SAAS,QAAQ;AACvB,cAAM,QAAQ,OAAO,KAAK,YAAS,OAAM,OAAO,YAAY,QAAQ;AACpE,YAAI,OAAO;AACP,kBAAQ,QAAQ;AAAA,YACZ,SAAS,0CAA0C;AAAA;AAAA;AAAA;AAAA;AAKnE,QAAI,kBAAkB;AACtB,QAAI,mBAAmB;AAAA;AAAA;AAG/B,wBAAwB,KAAK;AACzB,MAAI,IAAI,UAAU;AACd,WAAO,IAAI,aAAa,MAAM;AAAA,SAE7B;AACD,WAAO,IAAI,aAAa,MAAM;AAAA;AAAA;AAGtC,mDAAmD,OAAO;AACtD,QAAM,EAAE,WAAW;AACnB,QAAM,SAAS;AAAA,IACX,EAAE,UAAU,OAAO,KAAK,QAAQ,OAAO,OAAO;AAAA;AAElD,MAAI,OAAO,QAAQ,MAAM;AACrB,WAAO,KAAK;AAAA,MACR,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO,OAAO;AAAA;AAAA;AAGtB,SAAO,KAAK,EAAE,UAAU,OAAO,KAAK,UAAU,OAAO,MAAM;AAC3D,MAAI,MAAM,KAAK,QAAQ;AACnB,WAAO,KAAK;AAAA,MACR,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO;AAAA,QACH,SAAS;AAAA,UACL,MAAM;AAAA,UACN,UAAU;AAAA,UACV,SAAS,MAAM,KACV,IAAI,SAAO,GAAG,IAAI,OAAO,eAAe,QACxC,KAAK;AAAA,UACV,SAAS;AAAA,UACT,OAAO,MAAM;AAAA;AAAA;AAAA;AAAA;AAK7B,MAAI,OAAO,YAAY,MAAM;AACzB,WAAO,KAAK;AAAA,MACR,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO,OAAO;AAAA;AAAA;AAGtB,MAAI,MAAM,MAAM,QAAQ;AACpB,WAAO,KAAK;AAAA,MACR,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO,MAAM,MAAM,IAAI,WAAS,MAAM,OAAO;AAAA;AAAA;AAGrD,MAAI,OAAO,KAAK,MAAM,OAAO,MAAM,QAAQ;AACvC,WAAO,KAAK;AAAA,MACR,UAAU;AAAA,MACV,KAAK;AAAA,MACL,OAAO,MAAM,OAAO;AAAA;AAAA;AAG5B,SAAO,KAAK;AAAA,IACR,KAAK;AAAA,IACL,UAAU;AAAA,IACV,OAAO;AAAA,MACH,SAAS;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,QACV,SAAS,MAAM,MAAM,IAAI,WAAS,MAAM,KAAK,OAAO,KAAK;AAAA,QACzD,SAAS;AAAA,QACT,OAAO,MAAM;AAAA;AAAA;AAAA;AAIzB,SAAO;AAAA;AAKX,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,aAAa;AAEnB,IAAM,OAAO;AACb,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,uCAAuC,OAAO;AAC1C,QAAM,OAAO;AACb,QAAM,EAAE,WAAW;AACnB,MAAI,OAAO,QAAQ,MAAM;AACrB,SAAK,KAAK;AAAA,MACN,OAAO,OAAO,OAAO;AAAA,MACrB,WAAW;AAAA,MACX,iBAAiB;AAAA;AAAA;AAGzB,MAAI,OAAO,SAAS;AAChB,SAAK,KAAK;AAAA,MACN,OAAO;AAAA,MACP,WAAW;AAAA,MACX,iBAAiB;AAAA;AAAA;AAGzB,MAAI,MAAM,YAAY;AAClB,SAAK,KAAK;AAAA,MACN,OAAO;AAAA,MACP,WAAW;AAAA,MACX,iBAAiB;AAAA;AAAA;AAGzB,MAAI,MAAM,kBAAkB;AACxB,SAAK,KAAK;AAAA,MACN,OAAO;AAAA,MACP,WAAW;AAAA,MACX,iBAAiB;AAAA;AAAA;AAGzB,MAAI,MAAM,aAAa;AACnB,SAAK,KAAK;AAAA,MACN,OAAO;AAAA,MACP,WAAW;AAAA,MACX,iBAAiB;AAAA;AAAA;AAGzB,MAAI,OAAO,UAAU;AACjB,SAAK,KAAK;AAAA,MACN,OAAO,OAAO,OAAO,aAAa,WAC5B,aAAa,OAAO,aACpB;AAAA,MACN,WAAW;AAAA,MACX,iBAAiB;AAAA;AAAA;AAKzB,MAAI,KAAK,OAAO;AAChB,MAAI,MAAM,MAAM;AACZ,SAAK,OAAO;AACZ,WAAO,UAAU;AAAA;AAErB,SAAO;AAAA,IACH;AAAA,IACA,OAAO,OAAO;AAAA,IACd;AAAA,IACA,UAAU,MAAM,SAAS,IAAI;AAAA;AAAA;AAIrC,IAAI,gBAAgB;AACpB,IAAM,oBAAoB;AAC1B,+BAA+B,OAAO,cAAc;AAGhD,QAAM,gBAAgB,aAAa,QAAQ,UACvC,kBAAkB,aAAa,QAAQ,aAAa,QAAQ,SAAS,IAAI,MAAM;AACnF,QAAM,mBAAmB,MAAM,cAAc;AAC7C,MAAI,CAAC,eAAe;AAChB,UAAM,cAAc,aAAa,QAAQ,KAAK,WAAS,kBAAkB,OAAO,MAAM;AAAA;AAE1F,QAAM,SAAS,QAAQ,gBAAc,sBAAsB,YAAY;AAAA;AAE3E,sCAAsC,OAAO;AACzC,QAAM,aAAa;AACnB,QAAM,SAAS,QAAQ;AAAA;AAE3B,yBAAyB,OAAO,QAAQ;AACpC,QAAM,QAAQ,OAAO,MAAM,IAAI,MAAM;AACrC,QAAM,aAAa;AACnB,MAAI,CAAC,SAAS,MAAM,SAAS,GAAG;AAC5B,WAAO;AAAA;AAGX,QAAM,cAAc,IAAI,OAAO,MAAM,GAAG,QAAQ,OAAO,KAAK,MAAM;AAClE,MAAI,YAAY,KAAK,SAAS;AAE1B,UAAM,SAAS,QAAQ,WAAS,gBAAgB,OAAO;AAEvD,QAAI,MAAM,OAAO,SAAS,OAAO,WAAW,KAAK;AAC7C,YAAM,aAAa,MAAM,GAAG,KAAK;AACjC,aAAO;AAAA;AAGX,WAAO;AAAA;AAEX,QAAM,OAAO,MAAM,OAAO,KAAK;AAC/B,QAAM,cAAc,OAAO;AAE3B,MAAI,CAAC,OAAO,WAAW,QAClB,aAAY,SAAS,WAAW,KAAK,SAAS;AAC/C,WAAO;AACX,MAAI,YAAY,WAAW,WAAW,KAAK,WAAW;AAClD,WAAO;AACX,MAAI,MAAM,OAAO,QAAQ,OAAO,MAAM,OAAO,MAAM,SAAS;AACxD,WAAO;AACX,SAAO,MAAM,SAAS,KAAK,WAAS,gBAAgB,OAAO;AAAA;AAE/D,cAAc,KAAK,MAAM;AACrB,QAAM,MAAM;AACZ,aAAW,OAAO,KAAK;AACnB,QAAI,CAAC,KAAK,SAAS,MAAM;AAErB,UAAI,OAAO,IAAI;AAAA;AAAA;AAGvB,SAAO;AAAA;AAQX,sBAAsB,SAAS;AAC3B,QAAM,UAAU,oBAAoB,QAAQ,QAAQ;AACpD,QAAM,eAAe,QAAQ,cAAc;AAC3C,QAAM,mBAAmB,QAAQ,kBAAkB;AACnD,QAAM,gBAAgB,QAAQ;AAC9B,MAA+C,CAAC;AAC5C,UAAM,IAAI,MAAM;AAEpB,QAAM,eAAe;AACrB,QAAM,sBAAsB;AAC5B,QAAM,cAAc;AACpB,QAAM,eAAe,WAAW;AAChC,MAAI,kBAAkB;AAEtB,MAAI,aAAa,QAAQ,kBAAkB,uBAAuB,SAAS;AACvE,YAAQ,oBAAoB;AAAA;AAEhC,QAAM,kBAAkB,cAAc,KAAK,MAAM,gBAAc,KAAK;AACpE,QAAM,eAAe,cAAc,KAAK,MAAM;AAC9C,QAAM,eAEN,cAAc,KAAK,MAAM;AACzB,oBAAkB,eAAe,OAAO;AACpC,QAAI;AACJ,QAAI;AACJ,QAAI,YAAY,gBAAgB;AAC5B,eAAS,QAAQ,iBAAiB;AAClC,UAA+C,CAAC,QAAQ;AACpD,aAAK,iBAAiB,OAAO,qDAAqD;AAAA;AAEtF,eAAS;AAAA,WAER;AACD,eAAS;AAAA;AAEb,WAAO,QAAQ,SAAS,QAAQ;AAAA;AAEpC,uBAAqB,MAAM;AACvB,UAAM,gBAAgB,QAAQ,iBAAiB;AAC/C,QAAI,eAAe;AACf,cAAQ,YAAY;AAAA,eAEd,MAAwC;AAC9C,WAAK,qCAAqC,OAAO;AAAA;AAAA;AAGzD,uBAAqB;AACjB,WAAO,QAAQ,YAAY,IAAI,kBAAgB,aAAa;AAAA;AAEhE,oBAAkB,MAAM;AACpB,WAAO,CAAC,CAAC,QAAQ,iBAAiB;AAAA;AAEtC,mBAAiB,aAAa,iBAAiB;AAI3C,sBAAkB,OAAO,IAAI,mBAAmB,aAAa;AAC7D,QAAI,OAAO,gBAAgB,UAAU;AACjC,YAAM,qBAAqB,SAAS,cAAc,aAAa,gBAAgB;AAC/E,YAAM,gBAAe,QAAQ,QAAQ,EAAE,MAAM,mBAAmB,QAAQ;AACxE,YAAM,QAAO,cAAc,WAAW,mBAAmB;AACzD,UAAK,MAAwC;AACzC,YAAI,MAAK,WAAW;AAChB,eAAK,aAAa,6BAA6B;AAAA,iBAC1C,CAAC,cAAa,QAAQ,QAAQ;AACnC,eAAK,0CAA0C;AAAA;AAAA;AAIvD,aAAO,OAAO,oBAAoB,eAAc;AAAA,QAC5C,QAAQ,aAAa,cAAa;AAAA,QAClC,MAAM,OAAO,mBAAmB;AAAA,QAChC,gBAAgB;AAAA,QAChB;AAAA;AAAA;AAGR,QAA+C,CAAC,gBAAgB,cAAc;AAC1E,WAAK;AAAA,cAA+F;AACpG,aAAO,QAAQ;AAAA;AAEnB,QAAI;AAEJ,QAAI,YAAY,QAAQ,MAAM;AAC1B,UAAK,AACD,YAAY,eACZ,CAAE,WAAU,gBAEZ,OAAO,KAAK,YAAY,QAAQ,QAAQ;AACxC,aAAK,SAAS,YAAY;AAAA;AAE9B,wBAAkB,OAAO,IAAI,aAAa;AAAA,QACtC,MAAM,SAAS,cAAc,YAAY,MAAM,gBAAgB,MAAM;AAAA;AAAA,WAGxE;AAED,YAAM,eAAe,OAAO,IAAI,YAAY;AAC5C,iBAAW,OAAO,cAAc;AAC5B,YAAI,aAAa,QAAQ,MAAM;AAC3B,iBAAO,aAAa;AAAA;AAAA;AAI5B,wBAAkB,OAAO,IAAI,aAAa;AAAA,QACtC,QAAQ,aAAa;AAAA;AAIzB,sBAAgB,SAAS,aAAa,gBAAgB;AAAA;AAE1D,UAAM,eAAe,QAAQ,QAAQ,iBAAiB;AACtD,UAAM,OAAO,YAAY,QAAQ;AACjC,QAAK,AAA0C,QAAQ,CAAC,KAAK,WAAW,MAAM;AAC1E,WAAK,mEAAmE,gBAAgB;AAAA;AAI5F,iBAAa,SAAS,gBAAgB,aAAa,aAAa;AAChE,UAAM,WAAW,aAAa,kBAAkB,OAAO,IAAI,aAAa;AAAA,MACpE,MAAM,WAAW;AAAA,MACjB,MAAM,aAAa;AAAA;AAEvB,UAAM,OAAO,cAAc,WAAW;AACtC,QAAK,MAAwC;AACzC,UAAI,KAAK,WAAW,OAAO;AACvB,aAAK,aAAa,6BAA6B;AAAA,iBAE1C,CAAC,aAAa,QAAQ,QAAQ;AACnC,aAAK,0CAA0C,YAAY,QAAQ,OAAO,YAAY,OAAO;AAAA;AAAA;AAGrG,WAAO,OAAO;AAAA,MACV;AAAA,MAGA;AAAA,MACA,OAMA,qBAAqB,iBACf,eAAe,YAAY,SAC1B,YAAY,SAAS;AAAA,OAC7B,cAAc;AAAA,MACb,gBAAgB;AAAA,MAChB;AAAA;AAAA;AAGR,4BAA0B,IAAI;AAC1B,WAAO,OAAO,OAAO,WACf,SAAS,cAAc,IAAI,aAAa,MAAM,QAC9C,OAAO,IAAI;AAAA;AAErB,mCAAiC,IAAI,MAAM;AACvC,QAAI,oBAAoB,IAAI;AACxB,aAAO,kBAAkB,GAAyC;AAAA,QAC9D;AAAA,QACA;AAAA;AAAA;AAAA;AAIZ,gBAAc,IAAI;AACd,WAAO,iBAAiB;AAAA;AAE5B,mBAAiB,IAAI;AACjB,WAAO,KAAK,OAAO,iBAAiB,KAAK,EAAE,SAAS;AAAA;AAExD,gCAA8B,IAAI;AAC9B,UAAM,cAAc,GAAG,QAAQ,GAAG,QAAQ,SAAS;AACnD,QAAI,eAAe,YAAY,UAAU;AACrC,YAAM,EAAE,aAAa;AACrB,UAAI,oBAAoB,OAAO,aAAa,aAAa,SAAS,MAAM;AACxE,UAAI,OAAO,sBAAsB,UAAU;AACvC,4BACI,kBAAkB,SAAS,QAAQ,kBAAkB,SAAS,OACvD,oBAAoB,iBAAiB,qBAEpC,EAAE,MAAM;AAGpB,0BAAkB,SAAS;AAAA;AAE/B,UAAK,AACD,kBAAkB,QAAQ,QAC1B,CAAE,WAAU,oBAAoB;AAChC,aAAK;AAAA,EAA4B,KAAK,UAAU,mBAAmB,MAAM;AAAA,uBAA4B,GAAG;AACxG,cAAM,IAAI,MAAM;AAAA;AAEpB,aAAO,OAAO;AAAA,QACV,OAAO,GAAG;AAAA,QACV,MAAM,GAAG;AAAA,QAET,QAAQ,kBAAkB,QAAQ,OAAO,KAAK,GAAG;AAAA,SAClD;AAAA;AAAA;AAGX,4BAA0B,IAAI,gBAAgB;AAC1C,UAAM,iBAAkB,kBAAkB,QAAQ;AAClD,UAAM,OAAO,aAAa;AAC1B,UAAM,OAAO,GAAG;AAChB,UAAM,QAAQ,GAAG;AAEjB,UAAM,WAAU,GAAG,YAAY;AAC/B,UAAM,iBAAiB,qBAAqB;AAC5C,QAAI;AACA,aAAO,iBAAiB,OAAO,iBAAiB,iBAAiB;AAAA,QAC7D,OAAO,OAAO,mBAAmB,WAC3B,OAAO,IAAI,MAAM,eAAe,SAChC;AAAA,QACN;AAAA,QACA;AAAA,UAGJ,kBAAkB;AAEtB,UAAM,aAAa;AACnB,eAAW,iBAAiB;AAC5B,QAAI;AACJ,QAAI,CAAC,SAAS,oBAAoB,kBAAkB,MAAM,iBAAiB;AACvE,gBAAU,kBAAkB,IAA2C,EAAE,IAAI,YAAY;AAEzF,mBAAa,MAAM,MAGnB,MAGA;AAAA;AAEJ,WAAQ,WAAU,QAAQ,QAAQ,WAAW,SAAS,YAAY,OAC7D,MAAM,CAAC,UAAU,oBAAoB,SAElC,oBAAoB,OAAO,KACrB,QACA,YAAY,SAElB,aAAa,OAAO,YAAY,OACnC,KAAK,CAAC,aAAY;AACnB,UAAI,UAAS;AACT,YAAI,oBAAoB,UAAS,IAA+C;AAC5E,cAAK,AAED,oBAAoB,kBAAkB,QAAQ,SAAQ,KAAK,eAE3D,kBAEC,gBAAe,SAAS,eAAe,SAEhC,eAAe,SAAS,IAC1B,KAAK,IAAI;AACf,iBAAK,mFAAmF,KAAK,iBAAiB,WAAW;AAAA;AACzH,mBAAO,QAAQ,OAAO,IAAI,MAAM;AAAA;AAEpC,iBAAO,iBAEP,OAAO;AAAA,YAEH;AAAA,aACD,iBAAiB,SAAQ,KAAK;AAAA,YAC7B,OAAO,OAAO,SAAQ,OAAO,WACvB,OAAO,IAAI,MAAM,SAAQ,GAAG,SAC5B;AAAA,YACN;AAAA,cAGJ,kBAAkB;AAAA;AAAA,aAGrB;AAED,mBAAU,mBAAmB,YAAY,MAAM,MAAM,UAAS;AAAA;AAElE,uBAAiB,YAAY,MAAM;AACnC,aAAO;AAAA;AAAA;AAQf,4CAA0C,IAAI,MAAM;AAChD,UAAM,QAAQ,wBAAwB,IAAI;AAC1C,WAAO,QAAQ,QAAQ,OAAO,SAAS,QAAQ;AAAA;AAEnD,0BAAwB,IAAI;AACxB,UAAM,MAAM,cAAc,SAAS,OAAO;AAE1C,WAAO,OAAO,OAAO,IAAI,mBAAmB,aACtC,IAAI,eAAe,MACnB;AAAA;AAGV,oBAAkB,IAAI,MAAM;AACxB,QAAI;AACJ,UAAM,CAAC,gBAAgB,iBAAiB,mBAAmB,uBAAuB,IAAI;AAEtF,aAAS,wBAAwB,eAAe,WAAW,oBAAoB,IAAI;AAEnF,eAAW,UAAU,gBAAgB;AACjC,aAAO,YAAY,QAAQ,WAAS;AAChC,eAAO,KAAK,iBAAiB,OAAO,IAAI;AAAA;AAAA;AAGhD,UAAM,0BAA0B,iCAAiC,KAAK,MAAM,IAAI;AAChF,WAAO,KAAK;AAEZ,WAAQ,cAAc,QACjB,KAAK,MAAM;AAEZ,eAAS;AACT,iBAAW,SAAS,aAAa,QAAQ;AACrC,eAAO,KAAK,iBAAiB,OAAO,IAAI;AAAA;AAE5C,aAAO,KAAK;AACZ,aAAO,cAAc;AAAA,OAEpB,KAAK,MAAM;AAEZ,eAAS,wBAAwB,iBAAiB,qBAAqB,IAAI;AAC3E,iBAAW,UAAU,iBAAiB;AAClC,eAAO,aAAa,QAAQ,WAAS;AACjC,iBAAO,KAAK,iBAAiB,OAAO,IAAI;AAAA;AAAA;AAGhD,aAAO,KAAK;AAEZ,aAAO,cAAc;AAAA,OAEpB,KAAK,MAAM;AAEZ,eAAS;AACT,iBAAW,UAAU,iBAAiB;AAElC,YAAI,OAAO,aAAa;AACpB,cAAI,QAAQ,OAAO,cAAc;AAC7B,uBAAW,eAAe,OAAO;AAC7B,qBAAO,KAAK,iBAAiB,aAAa,IAAI;AAAA,iBAEjD;AACD,mBAAO,KAAK,iBAAiB,OAAO,aAAa,IAAI;AAAA;AAAA;AAAA;AAIjE,aAAO,KAAK;AAEZ,aAAO,cAAc;AAAA,OAEpB,KAAK,MAAM;AAGZ,SAAG,QAAQ,QAAQ,YAAW,OAAO,iBAAiB;AAEtD,eAAS,wBAAwB,iBAAiB,oBAAoB,IAAI,MAAM;AAChF,aAAO,KAAK;AAEZ,aAAO,cAAc;AAAA,OAEpB,KAAK,MAAM;AAEZ,eAAS;AACT,iBAAW,SAAS,oBAAoB,QAAQ;AAC5C,eAAO,KAAK,iBAAiB,OAAO,IAAI;AAAA;AAE5C,aAAO,KAAK;AACZ,aAAO,cAAc;AAAA,OAGpB,MAAM,SAAO,oBAAoB,KAAK,KACrC,MACA,QAAQ,OAAO;AAAA;AAEzB,4BAA0B,IAAI,MAAM,SAAS;AAGzC,gBACK,OACA,QAAQ,WAAS,eAAe,MAAM,MAAM,IAAI,MAAM;AAAA;AAO/D,8BAA4B,YAAY,MAAM,QAAQ,UAAS,MAAM;AAEjE,UAAM,QAAQ,wBAAwB,YAAY;AAClD,QAAI;AACA,aAAO;AAEX,UAAM,oBAAoB,SAAS;AACnC,UAAM,QAAQ,CAAC,YAAY,KAAK,QAAQ;AAGxC,QAAI,QAAQ;AAGR,UAAI,YAAW;AACX,sBAAc,QAAQ,WAAW,UAAU,OAAO;AAAA,UAC9C,QAAQ,qBAAqB,SAAS,MAAM;AAAA,WAC7C;AAAA;AAEH,sBAAc,KAAK,WAAW,UAAU;AAAA;AAGhD,iBAAa,QAAQ;AACrB,iBAAa,YAAY,MAAM,QAAQ;AACvC;AAAA;AAEJ,MAAI;AAEJ,4BAA0B;AAEtB,QAAI;AACA;AACJ,4BAAwB,cAAc,OAAO,CAAC,IAAI,OAAO,SAAS;AAC9D,UAAI,CAAC,OAAO;AACR;AAEJ,YAAM,aAAa,QAAQ;AAI3B,YAAM,iBAAiB,qBAAqB;AAC5C,UAAI,gBAAgB;AAChB,yBAAiB,OAAO,gBAAgB,EAAE,SAAS,MAAM,OAAO,SAAS,YAAY,MAAM;AAC3F;AAAA;AAEJ,wBAAkB;AAClB,YAAM,OAAO,aAAa;AAE1B,UAAI,WAAW;AACX,2BAAmB,aAAa,KAAK,UAAU,KAAK,QAAQ;AAAA;AAEhE,eAAS,YAAY,MAChB,MAAM,CAAC,UAAU;AAClB,YAAI,oBAAoB,OAAO,IAAwC,IAA0C;AAC7G,iBAAO;AAAA;AAEX,YAAI,oBAAoB,OAAO,IAA+C;AAU1E,2BAAiB,OAAO,iBAAiB,MAAM,KAAK;AAAA,YAChD,OAAO;AAAA,cACP,YAGC,KAAK,aAAW;AAIjB,gBAAI,oBAAoB,SAAS,IAC7B,OACA,CAAC,KAAK,SACN,KAAK,SAAS,eAAe,KAAK;AAClC,4BAAc,GAAG,IAAI;AAAA;AAAA,aAGxB,MAAM;AAEX,iBAAO,QAAQ;AAAA;AAGnB,YAAI,KAAK,OAAO;AACZ,wBAAc,GAAG,CAAC,KAAK,OAAO;AAAA;AAGlC,eAAO,aAAa,OAAO,YAAY;AAAA,SAEtC,KAAK,CAAC,YAAY;AACnB,kBACI,WACI,mBAEA,YAAY,MAAM;AAE1B,YAAI,SAAS;AACT,cAAI,KAAK,SAGL,CAAC,oBAAoB,SAAS,IAA0C;AACxE,0BAAc,GAAG,CAAC,KAAK,OAAO;AAAA,qBAEzB,KAAK,SAAS,eAAe,OAClC,oBAAoB,SAAS,IAAwC,KAA4C;AAGjH,0BAAc,GAAG,IAAI;AAAA;AAAA;AAG7B,yBAAiB,YAAY,MAAM;AAAA,SAGlC,MAAM;AAAA;AAAA;AAInB,MAAI,gBAAgB;AACpB,MAAI,iBAAiB;AACrB,MAAI;AASJ,wBAAsB,OAAO,IAAI,MAAM;AACnC,gBAAY;AACZ,UAAM,OAAO,eAAe;AAC5B,QAAI,KAAK,QAAQ;AACb,WAAK,QAAQ,aAAW,QAAQ,OAAO,IAAI;AAAA,WAE1C;AACD,UAAK,MAAwC;AACzC,aAAK;AAAA;AAET,cAAQ,MAAM;AAAA;AAGlB,WAAO,QAAQ,OAAO;AAAA;AAE1B,qBAAmB;AACf,QAAI,SAAS,aAAa,UAAU;AAChC,aAAO,QAAQ;AACnB,WAAO,IAAI,QAAQ,CAAC,UAAS,WAAW;AACpC,oBAAc,IAAI,CAAC,UAAS;AAAA;AAAA;AAGpC,uBAAqB,KAAK;AACtB,QAAI,CAAC,OAAO;AAER,cAAQ,CAAC;AACT;AACA,oBACK,OACA,QAAQ,CAAC,CAAC,UAAS,YAAa,MAAM,OAAO,OAAO;AACzD,oBAAc;AAAA;AAElB,WAAO;AAAA;AAGX,wBAAsB,IAAI,MAAM,QAAQ,mBAAmB;AACvD,UAAM,EAAE,mBAAmB;AAC3B,QAAI,CAAC,aAAa,CAAC;AACf,aAAO,QAAQ;AACnB,UAAM,iBAAkB,CAAC,UAAU,uBAAuB,aAAa,GAAG,UAAU,OAC9E,sBAAqB,CAAC,WACpB,QAAQ,SACR,QAAQ,MAAM,UAClB;AACJ,WAAO,WACF,KAAK,MAAM,eAAe,IAAI,MAAM,iBACpC,KAAK,cAAY,YAAY,iBAAiB,WAC9C,MAAM,SAAO,aAAa,KAAK,IAAI;AAAA;AAE5C,QAAM,KAAK,CAAC,UAAU,cAAc,GAAG;AACvC,MAAI;AACJ,QAAM,gBAAgB,IAAI;AAC1B,QAAM,SAAS;AAAA,IACX;AAAA,IACA,WAAW;AAAA,IACX;AAAA,IACA;AAAA,IACA,aAAa,QAAQ;AAAA,IACrB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM,MAAM,GAAG;AAAA,IACf,SAAS,MAAM,GAAG;AAAA,IAClB,YAAY,aAAa;AAAA,IACzB,eAAe,oBAAoB;AAAA,IACnC,WAAW,YAAY;AAAA,IACvB,SAAS,eAAe;AAAA,IACxB;AAAA,IACA,QAAQ,KAAK;AACT,YAAM,UAAS;AACf,UAAI,UAAU,cAAc;AAC5B,UAAI,UAAU,cAAc;AAC5B,UAAI,OAAO,iBAAiB,UAAU;AACtC,aAAO,eAAe,IAAI,OAAO,kBAAkB,UAAU;AAAA,QACzD,YAAY;AAAA,QACZ,KAAK,MAAM,MAAM;AAAA;AAKrB,UAAI,aAGA,CAAC,WACD,aAAa,UAAU,2BAA2B;AAElD,kBAAU;AACV,aAAK,cAAc,UAAU,MAAM,SAAO;AACtC,cAAK;AACD,iBAAK,8CAA8C;AAAA;AAAA;AAG/D,YAAM,gBAAgB;AACtB,iBAAW,OAAO,2BAA2B;AACzC,eAAO,eAAe,eAAe,KAAK;AAAA,UACtC,KAAK,MAAM,aAAa,MAAM;AAAA,UAC9B,YAAY;AAAA;AAAA;AAGpB,UAAI,QAAQ,WAAW;AACvB,UAAI,QAAQ,kBAAkB,gBAAgB;AAC9C,UAAI,QAAQ,uBAAuB;AACnC,YAAM,aAAa,IAAI;AACvB,oBAAc,IAAI;AAClB,UAAI,UAAU,WAAY;AACtB,sBAAc,OAAO;AAErB,YAAI,cAAc,OAAO,GAAG;AAExB,4BAAkB;AAClB,mCAAyB;AACzB,kCAAwB;AACxB,uBAAa,QAAQ;AACrB,oBAAU;AACV,kBAAQ;AAAA;AAEZ;AAAA;AAGJ,UAA0E,WAAW;AACjF,oBAAY,KAAK,SAAQ;AAAA;AAAA;AAAA;AAKrC,yBAAuB,QAAQ;AAC3B,WAAO,OAAO,OAAO,CAAC,SAAS,UAAU,QAAQ,KAAK,MAAM,eAAe,SAAS,QAAQ;AAAA;AAEhG,SAAO;AAAA;AAEX,gCAAgC,IAAI,MAAM;AACtC,QAAM,iBAAiB;AACvB,QAAM,kBAAkB;AACxB,QAAM,kBAAkB;AACxB,QAAM,MAAM,KAAK,IAAI,KAAK,QAAQ,QAAQ,GAAG,QAAQ;AACrD,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC1B,UAAM,aAAa,KAAK,QAAQ;AAChC,QAAI,YAAY;AACZ,UAAI,GAAG,QAAQ,KAAK,YAAU,kBAAkB,QAAQ;AACpD,wBAAgB,KAAK;AAAA;AAErB,uBAAe,KAAK;AAAA;AAE5B,UAAM,WAAW,GAAG,QAAQ;AAC5B,QAAI,UAAU;AAEV,UAAI,CAAC,KAAK,QAAQ,KAAK,YAAU,kBAAkB,QAAQ,YAAY;AACnE,wBAAgB,KAAK;AAAA;AAAA;AAAA;AAIjC,SAAO,CAAC,gBAAgB,iBAAiB;AAAA;AAO7C,qBAAqB;AACjB,SAAO,OAAO;AAAA;AAMlB,kBAAkB,OAAO;AACrB,SAAO,OAAO;AAAA;", "names": []}