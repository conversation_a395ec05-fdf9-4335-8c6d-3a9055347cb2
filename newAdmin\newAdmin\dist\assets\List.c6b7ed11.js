var e=Object.defineProperty,a=Object.getOwnPropertySymbols,l=Object.prototype.hasOwnProperty,r=Object.prototype.propertyIsEnumerable,o=(a,l,r)=>l in a?e(a,l,{enumerable:!0,configurable:!0,writable:!0,value:r}):a[l]=r;import{A as s,ab as t,C as d,a6 as u,ac as n,af as i,ai as p,aj as c,ad as m,ak as v,al as f,ae as g,ap as w,au as y,o as b,k as h,l as k,m as V,w as _,n as j,aw as C,ax as I,ay as P,v as x,av as U,H as z,L as O,J as E,I as L,aq as S,E as $,z as q}from"./vendor.db8f8423.js";/* empty css                   *//* empty css                   *//* empty css                     *//* empty css                    *//* empty css                  *//* empty css                      *//* empty css                 *//* empty css                        */import{i as T}from"./index.2f9d9633.js";import{g as B}from"./role.0416e54a.js";const D=()=>T.get("/user/list"),F=e=>T.get(`/user/get/${e}`),N=e=>T.post("/user/register",e),A=e=>T.put("/user/update",e),H=e=>T.delete(`/user/delete/${e}`);const J={class:"user-list-container"},K={class:"header-actions"},R={class:"pagination-container"},G={class:"dialog-footer"},M={__name:"List",setup(e){const T=s([]),M=s(!1),Q=s(""),W=s(1),X=s(10),Y=s(0),Z=s(!1),ee=s(!1),ae=s(!1),le=s([]),re=t({id:null,username:"",password:"",confirmPassword:"",nickname:"",phone:"",email:"",roleIds:[]}),oe=t({username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"长度在 3 到 20 个字符",trigger:"blur"}],password:[{validator:(e,a,l)=>{var r;ee.value||a?(""!==re.confirmPassword&&(null==(r=se.value)||r.validateField("confirmPassword")),l()):l(new Error("请输入密码"))},trigger:"blur"}],confirmPassword:[{validator:(e,a,l)=>{ee.value||a?a!==re.password?l(new Error("两次输入密码不一致")):l():l(new Error("请再次输入密码"))},trigger:"blur"}],phone:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号码",trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱地址",trigger:"blur"}],roleIds:[{required:!0,message:"请选择角色",trigger:"change",type:"array"}]}),se=s(null),te=async()=>{var e;try{M.value=!0;const a=await D();200===a.code?(T.value=a.data||[],Y.value=(null==(e=a.data)?void 0:e.length)||0,Q.value&&(T.value=T.value.filter((e=>{var a,l;return(null==(a=e.username)?void 0:a.toLowerCase().includes(Q.value.toLowerCase()))||(null==(l=e.nickname)?void 0:l.toLowerCase().includes(Q.value.toLowerCase()))})))):S.error(a.message||"获取用户列表失败")}catch(a){console.error("获取用户列表出错:",a),S.error(a.message||"获取用户列表失败")}finally{M.value=!1}},de=()=>{W.value=1,te()},ue=e=>{pe(),e?(ee.value=!0,ne(e.id)):ee.value=!1,Z.value=!0},ne=async e=>{try{ae.value=!0;const a=await F(e);200===a.code&&a.data?(Object.keys(re).forEach((e=>{"password"!==e&&"confirmPassword"!==e&&void 0!==a.data[e]&&(re[e]=a.data[e])})),a.data.roleIds?re.roleIds=a.data.roleIds:a.data.roleId&&(re.roleIds=a.data.roleId?[a.data.roleId]:[])):(S.error(a.message||"获取用户信息失败"),Z.value=!1)}catch(a){console.error("获取用户信息出错:",a),S.error(a.message||"获取用户信息失败"),Z.value=!1}finally{ae.value=!1}},ie=async()=>{se.value&&await se.value.validate((async e=>{if(e)try{ae.value=!0;const e=((e,s)=>{for(var t in s||(s={}))l.call(s,t)&&o(e,t,s[t]);if(a)for(var t of a(s))r.call(s,t)&&o(e,t,s[t]);return e})({},re);let s;delete e.confirmPassword,ee.value&&!e.password&&delete e.password,ee.value?(console.log(e),s=await A(e)):s=await N(e),200===s.code?(S.success((ee.value?"更新":"添加")+"成功"),Z.value=!1,te()):S.error(s.message||(ee.value?"更新":"添加")+"失败")}catch(s){console.error((ee.value?"更新":"添加")+"用户出错:",s),S.error(s.message||(ee.value?"更新":"添加")+"失败")}finally{ae.value=!1}}))},pe=()=>{se.value&&se.value.resetFields(),Object.keys(re).forEach((e=>{re[e]="id"===e?null:""}))},ce=e=>{X.value=e,te()},me=e=>{W.value=e,te()},ve=(e,a)=>{const l=e[a.property];if(!l)return"-";try{return new Date(l).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})}catch(r){return l}};return d((()=>{te(),(async()=>{try{const e=await B();console.log(e),200===e.code?le.value=e.data||[]:S.error(e.message||"获取角色列表失败")}catch(e){console.error("获取角色列表出错:",e),S.error(e.message||"获取角色列表失败")}})()})),(e,a)=>{const l=q,r=u,o=n,s=i,t=p,d=c,B=m,D=v,F=f,N=g,A=w,ne=y;return b(),h("div",J,[k("div",K,[V(o,{modelValue:Q.value,"onUpdate:modelValue":a[0]||(a[0]=e=>Q.value=e),placeholder:"请输入用户名搜索",clearable:"",class:"search-input",onClear:te,onKeyup:I(de,["enter"])},{append:_((()=>[V(r,{onClick:de},{default:_((()=>[V(l,null,{default:_((()=>[V(j(C))])),_:1})])),_:1})])),_:1},8,["modelValue"]),V(r,{type:"primary",onClick:a[1]||(a[1]=e=>ue())},{default:_((()=>[V(l,null,{default:_((()=>[V(j(P))])),_:1}),x("新增用户 ")])),_:1})]),U((b(),z(t,{data:T.value,border:"",style:{width:"100%"},"row-key":"id"},{default:_((()=>[V(s,{prop:"id",label:"ID",width:"80"}),V(s,{prop:"username",label:"用户名"}),V(s,{prop:"nickname",label:"昵称"}),V(s,{prop:"phone",label:"手机号"}),V(s,{prop:"email",label:"邮箱"}),V(s,{prop:"createTime",label:"创建时间",formatter:ve}),V(s,{label:"操作",width:"200",fixed:"right"},{default:_((({row:e})=>[V(r,{type:"primary",link:"",onClick:a=>ue(e)},{default:_((()=>[x(" 编辑 ")])),_:2},1032,["onClick"]),V(r,{type:"danger",link:"",onClick:a=>(e=>{$.confirm(`确定要删除用户 "${e.username||e.nickname||e.id}" 吗？`,"删除确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((async()=>{try{const a=await H(e.id);200===a.code?(S.success("删除成功"),te()):S.error(a.message||"删除失败")}catch(a){console.error("删除用户出错:",a),S.error(a.message||"删除失败")}})).catch((()=>{}))})(e)},{default:_((()=>[x(" 删除 ")])),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[ne,M.value]]),k("div",R,[V(d,{"current-page":W.value,"onUpdate:currentPage":a[2]||(a[2]=e=>W.value=e),"page-size":X.value,"onUpdate:pageSize":a[3]||(a[3]=e=>X.value=e),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:Y.value,onSizeChange:ce,onCurrentChange:me},null,8,["current-page","page-size","total"])]),V(A,{modelValue:Z.value,"onUpdate:modelValue":a[12]||(a[12]=e=>Z.value=e),title:ee.value?"编辑用户":"新增用户",width:"500px",onClosed:pe},{footer:_((()=>[k("span",G,[V(r,{onClick:a[11]||(a[11]=e=>Z.value=!1)},{default:_((()=>[x("取消")])),_:1}),V(r,{type:"primary",loading:ae.value,onClick:ie},{default:_((()=>[x("确定")])),_:1},8,["loading"])])])),default:_((()=>[V(N,{ref_key:"formRef",ref:se,model:re,rules:oe,"label-width":"100px",class:"user-form"},{default:_((()=>[V(B,{label:"用户名",prop:"username"},{default:_((()=>[V(o,{modelValue:re.username,"onUpdate:modelValue":a[4]||(a[4]=e=>re.username=e),placeholder:"请输入用户名"},null,8,["modelValue"])])),_:1}),ee.value?O("",!0):(b(),z(B,{key:0,label:"密码",prop:"password"},{default:_((()=>[V(o,{modelValue:re.password,"onUpdate:modelValue":a[5]||(a[5]=e=>re.password=e),type:"password",placeholder:"请输入密码","show-password":""},null,8,["modelValue"])])),_:1})),ee.value?O("",!0):(b(),z(B,{key:1,label:"确认密码",prop:"confirmPassword"},{default:_((()=>[V(o,{modelValue:re.confirmPassword,"onUpdate:modelValue":a[6]||(a[6]=e=>re.confirmPassword=e),type:"password",placeholder:"请确认密码","show-password":""},null,8,["modelValue"])])),_:1})),V(B,{label:"昵称",prop:"nickname"},{default:_((()=>[V(o,{modelValue:re.nickname,"onUpdate:modelValue":a[7]||(a[7]=e=>re.nickname=e),placeholder:"请输入昵称"},null,8,["modelValue"])])),_:1}),V(B,{label:"手机号",prop:"phone"},{default:_((()=>[V(o,{modelValue:re.phone,"onUpdate:modelValue":a[8]||(a[8]=e=>re.phone=e),placeholder:"请输入手机号"},null,8,["modelValue"])])),_:1}),V(B,{label:"邮箱",prop:"email"},{default:_((()=>[V(o,{modelValue:re.email,"onUpdate:modelValue":a[9]||(a[9]=e=>re.email=e),placeholder:"请输入邮箱"},null,8,["modelValue"])])),_:1}),V(B,{label:"角色",prop:"roleIds"},{default:_((()=>[V(F,{modelValue:re.roleIds,"onUpdate:modelValue":a[10]||(a[10]=e=>re.roleIds=e),placeholder:"请选择角色",multiple:"","collapse-tags":""},{default:_((()=>[(b(!0),h(E,null,L(le.value,(e=>(b(),z(D,{key:e.id,label:e.roleName,value:e.id},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})])),_:1},8,["model","rules"])])),_:1},8,["modelValue","title"])])}},__scopeId:"data-v-1f0aa244"};export{M as default};
