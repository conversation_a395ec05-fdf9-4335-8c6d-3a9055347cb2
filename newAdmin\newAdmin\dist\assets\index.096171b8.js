import{Z as e,A as a,C as l,ac as s,ad as r,a6 as o,ae as d,o as t,k as n,l as u,m as i,w as c,n as p,r as m,aZ as g,a_ as v,q as h,H as f,a$ as b,L as y,v as w,a9 as x,aa as C,aq as k,E as T}from"./vendor.db8f8423.js";/* empty css                     *//* empty css                 */const I={baseURL:"https://api.buzhiyushu.cn",timeout:1e4},V=e=>{const{baseURL:a}=I;return`${a}${e}`};const _={class:"register-page"},N={class:"register-container"},M={class:"register-form"},U=(e=>(x("data-v-6a402454"),e=e(),C(),e))((()=>u("div",{class:"form-header"},[u("h2",null,"与书同行")],-1))),z={class:"captcha-row"},S=["src"],j={__name:"index",setup(x){const C=e(),I=a(!1),j=a(),q=a(""),E=a(!0),P=a({pddMallId:"",pddMallName:"",type:"",accessToken:"",skuSpec:""}),$=a({username:"",phoneNumber:"",password:"",confirmPassword:"",inviteCode:"",code:"",uuid:"",clientId:"e5cd7e4891bf95d1d19206ce24a7b32e",grantType:"password",tenantId:"000000",userType:"sys_user"}),B={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:2,max:20,message:"用户名长度在2到20个字符",trigger:"blur"}],phoneNumber:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:5,max:20,message:"密码长度在5到20个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认密码",trigger:"blur"},{validator:(e,a,l)=>{a!==$.value.password?l(new Error("两次输入密码不一致")):l()},trigger:"blur"}],code:[{required:!0,message:"请输入验证码",trigger:"blur",validator:(e,a,l)=>{E.value?a?l():l(new Error("请输入验证码")):l()}}]},R=async()=>{try{const e=await fetch(V("/auth/code"),{method:"GET",headers:{"Content-Type":"application/json"}});if(e.ok){const a=e.headers.get("content-type");if(a&&a.includes("application/json")){const a=await e.json();if(200===a.code){const{data:e}=a;E.value=void 0===e.captchaEnabled||e.captchaEnabled,E.value?($.value.uuid=e.uuid,q.value="data:image/gif;base64,"+e.img):($.value.uuid="",q.value="")}else{let e=a.msg||"获取验证码失败";"Captcha error"===a.msg?e="验证码生成失败，请重试":("Captcha invalid"===a.msg||a.msg&&(a.msg.includes("Captcha")||a.msg.includes("captcha")))&&(e="验证码服务异常，请重试"),k.error(e)}}else{await e.text();k.error("服务器响应格式错误，请检查API接口")}}else k.error(`获取验证码失败 (${e.status})，请重试`)}catch(e){"SyntaxError"===e.name&&e.message.includes("JSON")?k.error("服务器响应格式错误，请检查API接口"):k.error("网络错误，获取验证码失败")}},A=async()=>{try{if(!(await j.value.validate()))return;I.value=!0;const e={username:$.value.username,password:$.value.password,phoneNumber:$.value.phoneNumber,inviteCode:$.value.inviteCode,clientId:$.value.clientId,grantType:$.value.grantType,tenantId:$.value.tenantId,userType:$.value.userType,pddMallId:P.value.pddMallId,pddMallName:P.value.pddMallName,pddType:P.value.type,accessToken:P.value.accessToken,skuSpec:P.value.skuSpec};E.value&&(e.code=$.value.code,e.uuid=$.value.uuid);const a=await fetch(V("/auth/register"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)}),l=await a.json();if(200===l.code)L();else{let e=l.msg||"注册失败，请重试";"Captcha error"===l.msg?e="验证码错误，请重新输入":"Captcha invalid"===l.msg?e="验证码无效，请重新输入":l.msg&&(l.msg.includes("Captcha")||l.msg.includes("captcha")||l.msg.includes("验证码"))&&(e="验证码错误，请重新输入"),k.error(e),E.value&&l.msg&&("Captcha error"===l.msg||"Captcha invalid"===l.msg||l.msg.includes("Captcha")||l.msg.includes("captcha")||l.msg.includes("验证码"))&&(R(),$.value.code="")}}catch(e){console.error("注册失败:",e),k.error("注册失败，请重试")}finally{I.value=!1}},L=()=>{const e="https://erp.buzhiyushu.cn/";T({title:"注册成功！",message:`\n            <div style="text-align: center;">\n                <p style="margin-bottom: 15px; color: #67C23A; font-size: 16px;">🎉 恭喜您注册成功！</p>\n                <p style="margin-bottom: 15px; color: #606266;">请复制以下链接进行登录：</p>\n                <div style="background: #f5f7fa; padding: 10px; border-radius: 4px; margin-bottom: 15px; word-break: break-all;">\n                    ${e}\n                </div>\n            </div>\n        `,dangerouslyUseHTMLString:!0,showCancelButton:!0,confirmButtonText:"复制链接",cancelButtonText:"关闭",confirmButtonClass:"el-button--primary",cancelButtonClass:"el-button--default",center:!0}).then((async()=>{try{await navigator.clipboard.writeText(e),k.success("链接已复制到剪贴板！")}catch(a){try{const a=document.createElement("textarea");a.value=e,document.body.appendChild(a),a.select(),document.execCommand("copy"),document.body.removeChild(a),k.success("链接已复制到剪贴板！")}catch(l){k.error("复制失败，请手动复制链接")}}})).catch((()=>{console.log("用户关闭了弹窗")}))};return l((()=>{(()=>{const e=C.query;P.value={pddMallId:e.pddMallId||"",pddMallName:decodeURIComponent(e.pddMallName||""),type:e.type||"",accessToken:e.accessToken||"",skuSpec:decodeURIComponent(e.skuSpec||"")},P.value.pddMallId&&($.value.username="pdd"+P.value.pddMallId)})(),R()})),(e,a)=>{const l=s,x=r,C=o,k=d;return t(),n("div",_,[u("div",N,[u("div",M,[U,i(k,{ref_key:"formRef",ref:j,model:$.value,rules:B,"label-width":"0"},{default:c((()=>[i(x,{prop:"username"},{default:c((()=>[i(l,{modelValue:$.value.username,"onUpdate:modelValue":a[0]||(a[0]=e=>$.value.username=e),placeholder:"用户名",size:"large","prefix-icon":p(m),disabled:""},null,8,["modelValue","prefix-icon"])])),_:1}),i(x,{prop:"phoneNumber"},{default:c((()=>[i(l,{modelValue:$.value.phoneNumber,"onUpdate:modelValue":a[1]||(a[1]=e=>$.value.phoneNumber=e),placeholder:"手机号",size:"large","prefix-icon":p(g)},null,8,["modelValue","prefix-icon"])])),_:1}),i(x,{prop:"password"},{default:c((()=>[i(l,{modelValue:$.value.password,"onUpdate:modelValue":a[2]||(a[2]=e=>$.value.password=e),type:"password",placeholder:"密码",size:"large","prefix-icon":p(v),"show-password":""},null,8,["modelValue","prefix-icon"])])),_:1}),i(x,{prop:"confirmPassword"},{default:c((()=>[i(l,{modelValue:$.value.confirmPassword,"onUpdate:modelValue":a[3]||(a[3]=e=>$.value.confirmPassword=e),type:"password",placeholder:"确认密码",size:"large","prefix-icon":p(v),"show-password":""},null,8,["modelValue","prefix-icon"])])),_:1}),i(x,{prop:"inviteCode"},{default:c((()=>[i(l,{modelValue:$.value.inviteCode,"onUpdate:modelValue":a[4]||(a[4]=e=>$.value.inviteCode=e),placeholder:"邀请码(非必填)",size:"large","prefix-icon":p(h)},null,8,["modelValue","prefix-icon"])])),_:1}),E.value?(t(),f(x,{key:0,prop:"code"},{default:c((()=>[u("div",z,[i(l,{modelValue:$.value.code,"onUpdate:modelValue":a[5]||(a[5]=e=>$.value.code=e),placeholder:"验证码",size:"large","prefix-icon":p(b)},null,8,["modelValue","prefix-icon"]),u("div",{class:"captcha-image",onClick:R},[q.value?(t(),n("img",{key:0,src:q.value,alt:"验证码"},null,8,S)):y("",!0)])])])),_:1})):y("",!0),i(x,null,{default:c((()=>[i(C,{type:"primary",size:"large",class:"register-btn",onClick:A,loading:I.value},{default:c((()=>[w(" 注册 ")])),_:1},8,["loading"])])),_:1})])),_:1},8,["model"])])])])}},__scopeId:"data-v-6a402454"};export{j as default};
