import{g as a,ab as e,A as s,u as l,C as r,an as o,ac as t,ad as c,a6 as n,ae as i,o as d,k as p,m as u,w as m,l as f,t as g,n as h,v,ax as w,a9 as b,aa as y,aQ as _,aq as V}from"./vendor.db8f8423.js";/* empty css                     *//* empty css                 *//* empty css                   */import{s as x}from"./index.2f9d9633.js";const j={class:"login-container"},k={class:"header"},q=(a=>(b("data-v-f0d00b06"),a=a(),y(),a))((()=>f("h2",{class:"title"},"登录",-1))),C={class:"subtitle"},U={class:"captcha-wrapper"},D=["src"],I={__name:"Index",setup(b){var y;const I=null==(y=a())?void 0:y.appContext.config.globalProperties.$global,P=e({username:"",password:"",captcha:""}),R=e({username:[{required:!0,message:"用户名不能为空",trigger:"blur"}],password:[{required:!0,message:"密码不能为空",trigger:"blur"}],captcha:[{required:!0,message:"验证码不能为空",trigger:"blur"}]}),$=s(null),z=s(""),A=s(!1),F=l(),K=()=>{z.value=`https://newadmin.buzhiyushu.cn/admin/generateCaptcha?t=${Date.now()}`},L=async()=>{$.value&&await $.value.validate((async a=>{if(a)try{A.value=!0;const a=new FormData;a.append("username",P.username),a.append("password",P.password),a.append("code",P.captcha),console.log('准备调用store.dispatch("login")',a),await x.dispatch("login",a),console.log('store.dispatch("login")调用成功');const e=F.currentRoute.value.query.redirect||"/welcome";console.log("跳转地址",e),_((()=>{window.location.href=e,F.replace(e),V.success("登录成功")}))}catch(e){console.error("登录错误详情:",e),K(),V.error(e.message||"登录失败")}finally{A.value=!1}else K()}))};return r((()=>{K()})),(a,e)=>{const s=o,l=t,r=c,b=n,y=i;return d(),p("div",j,[u(y,{ref_key:"formRef",ref:$,model:P,rules:R,onKeyup:w(L,["enter"])},{default:m((()=>{var a;return[f("div",k,[q,f("p",C,"欢迎使用"+g(null==(a=h(I))?void 0:a.system.name)+"管理系统",1),u(s)]),u(r,{prop:"username",class:"form-item"},{default:m((()=>[u(l,{modelValue:P.username,"onUpdate:modelValue":e[0]||(e[0]=a=>P.username=a),placeholder:"请输入用户名","prefix-icon":"User",class:"input-item"},null,8,["modelValue"])])),_:1}),u(r,{prop:"password",class:"form-item"},{default:m((()=>[u(l,{modelValue:P.password,"onUpdate:modelValue":e[1]||(e[1]=a=>P.password=a),type:"password",placeholder:"请输入密码","prefix-icon":"Lock","show-password":"",class:"input-item"},null,8,["modelValue"])])),_:1}),u(r,{prop:"captcha",class:"form-item"},{default:m((()=>[f("div",U,[u(l,{modelValue:P.captcha,"onUpdate:modelValue":e[2]||(e[2]=a=>P.captcha=a),placeholder:"请输入验证码","prefix-icon":"Picture",class:"captcha-input input-item"},null,8,["modelValue"]),f("img",{src:z.value,class:"captcha-image",onClick:K},null,8,D)])])),_:1}),u(s,{class:"custom-divider"}),u(b,{type:"primary",class:"login-btn",loading:A.value,onClick:L},{default:m((()=>[v("登录")])),_:1},8,["loading"])]})),_:1},8,["model","rules"])])}},__scopeId:"data-v-f0d00b06"};export{I as default};
