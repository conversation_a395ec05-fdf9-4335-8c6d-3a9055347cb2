import{A as e,a6 as s,o as a,H as t,w as r,m as n,n as i,as as o,z as l}from"./vendor.db8f8423.js";const u=Object.assign({name:"RefreshButton"},{__name:"RefreshButton",props:{size:{type:String,default:"default"},position:{type:String,default:"right"}},emits:["refresh"],setup(u,{emit:f}){const d=f,c=e(!1),p=()=>{c.value=!0,d("refresh"),setTimeout((()=>{c.value=!1}),500)};return(e,f)=>{const d=l,m=s;return a(),t(m,{class:"btn-circle btn-refresh",size:u.size,onClick:p,loading:c.value},{default:r((()=>[n(d,null,{default:r((()=>[n(i(o))])),_:1})])),_:1},8,["size","loading"])}}});u.__scopeId="data-v-4afcc2ad";var f=u;export{f as R};
