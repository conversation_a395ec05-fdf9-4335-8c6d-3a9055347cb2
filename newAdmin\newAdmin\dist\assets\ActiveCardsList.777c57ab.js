import{A as a,ab as e,C as t,aq as r,af as s,ah as i,ai as o,aj as n,au as l,o as d,k as c,m as p,av as u,H as f,w as m,v as b,t as v,l as g,a9 as w,aa as h}from"./vendor.db8f8423.js";/* empty css                   *//* empty css                      *//* empty css                    *//* empty css                  *//* empty css                 *//* empty css                        */import{A as j}from"./ActionBar.136c7dba.js";import{c as _}from"./cards.ff03880b.js";import"./RefreshButton.54bbaca3.js";import"./index.2f9d9633.js";const z={class:"active-cards-container"},y=(a=>(w("data-v-0b6b9e2f"),a=a(),h(),a))((()=>g("div",{class:"status-indicator active"},null,-1))),x={class:"pagination-container"},C={__name:"ActiveCardsList",setup(w){const h=a([]),C=a(!1),A=e({current:1,size:10,total:0});t((()=>{S()}));const S=async()=>{C.value=!0;try{const a=await _.getActiveCardsPage(A);200===a.code?(h.value=a.data.list||[],A.total=a.data.total||0):r.error(a.message||"获取数据失败")}catch(a){console.error("获取数据失败:",a),r.error("获取数据失败，请稍后再试"),h.value=[],A.total=0}finally{C.value=!1}},T=()=>{S()},D=a=>{A.size=a,A.current=1,S()},k=a=>{A.current=a,S()},B=a=>({0:"未激活",1:"未使用",2:"已使用",3:"已冻结",4:"已过期"}[a]||"未知"),I=a=>{if(!a)return"-";return new Date(a).toLocaleString()};return(a,e)=>{const t=s,r=i,w=o,_=n,S=l;return d(),c("div",z,[p(j,{onRefresh:T}),u((d(),f(w,{data:h.value,border:"",stripe:"",style:{width:"100%"}},{default:m((()=>[p(t,{label:"状态",width:"80",align:"center"},{default:m((()=>[y])),_:1}),p(t,{prop:"cardId",label:"卡密账号","min-width":"180"}),p(t,{prop:"cardSecret",label:"卡密密码","min-width":"180"}),p(t,{prop:"status",label:"使用状态",width:"120"},{default:m((({row:a})=>{return[p(r,{type:(e=a.status,{0:"info",1:"success",2:"warning",3:"danger",4:"info"}[e]||"info")},{default:m((()=>[b(v(B(a.status)),1)])),_:2},1032,["type"])];var e})),_:1}),p(t,{prop:"effectiveDays",label:"有效期",width:"100"},{default:m((({row:a})=>[b(v(a.effectiveDays)+"天 ",1)])),_:1}),p(t,{prop:"activateTime",label:"激活时间","min-width":"160"},{default:m((({row:a})=>[b(v(I(a.activateTime)),1)])),_:1}),p(t,{prop:"expireTime",label:"过期时间","min-width":"160"},{default:m((({row:a})=>[b(v(I(a.expireTime)),1)])),_:1}),p(t,{prop:"note",label:"备注","min-width":"120"})])),_:1},8,["data"])),[[S,C.value]]),g("div",x,[p(_,{"current-page":A.current,"onUpdate:currentPage":e[0]||(e[0]=a=>A.current=a),"page-size":A.size,"onUpdate:pageSize":e[1]||(e[1]=a=>A.size=a),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:A.total,onSizeChange:D,onCurrentChange:k},null,8,["current-page","page-size","total"])])])}},__scopeId:"data-v-0b6b9e2f"};export{C as default};
