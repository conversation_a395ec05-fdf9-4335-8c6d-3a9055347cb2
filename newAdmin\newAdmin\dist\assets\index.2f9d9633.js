var e=Object.defineProperty,t=Object.defineProperties,o=Object.getOwnPropertyDescriptors,s=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,n=Object.prototype.propertyIsEnumerable,a=(t,o,s)=>o in t?e(t,o,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[o]=s;import{a as i,c,E as l,d as m,g as d,u,b as p,e as h,f as _,h as f,i as g,j as k,o as T,k as v,l as E,m as S,w as b,n as y,p as I,t as A,s as O,q as w,r as L,v as P,x as R,y as j,z as N,A as C,B as x,C as D,D as V,F as W,G as U,H as $,I as M,J as z,K as F,L as B,M as H,N as J,O as K,P as q,Q as G,R as Q,S as X,T as Y,U as Z,V as ee,W as te,X as oe,Y as se,Z as re,_ as ne,$ as ae,a0 as ie,a1 as ce}from"./vendor.db8f8423.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const o of e)if("childList"===o.type)for(const e of o.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerpolicy&&(t.referrerPolicy=e.referrerpolicy),"use-credentials"===e.crossorigin?t.credentials="include":"anonymous"===e.crossorigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const le=i.create({baseURL:"/api",timeout:3e4});const me=new class{constructor(){this.socket=null,this.isConnected=!1,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.reconnectInterval=3e3,this.listeners=new Map}connect(){if(!this.socket||this.socket.readyState!==WebSocket.OPEN&&this.socket.readyState!==WebSocket.CONNECTING)try{const e=localStorage.getItem("accessToken");if(!e)return void console.error("WebSocket连接失败: 未找到accessToken");this.socket=new WebSocket("ws://146.56.192.164:9090/ws?token="+e),this.socket.onopen=()=>{console.log("WebSocket连接已建立"),this.isConnected=!0,this.reconnectAttempts=0,this.sendMessage({type:"auth",token:e}),this.triggerEvent("connect")},this.socket.onmessage=e=>{try{const t=JSON.parse(e.data);console.log("收到WebSocket消息:",t),this.triggerEvent("message",t)}catch(t){console.error("解析WebSocket消息失败:",t),this.triggerEvent("error",t)}},this.socket.onclose=e=>{console.log("WebSocket连接已关闭:",e),this.isConnected=!1,this.triggerEvent("disconnect"),this.attemptReconnect()},this.socket.onerror=e=>{console.error("WebSocket错误:",e),this.triggerEvent("error",e)}}catch(e){console.error("初始化WebSocket失败:",e),this.triggerEvent("error",e)}else console.log("WebSocket已连接或正在连接中")}attemptReconnect(){this.reconnectAttempts>=this.maxReconnectAttempts?console.log("达到最大重连次数"):(this.reconnectAttempts++,console.log(`尝试第 ${this.reconnectAttempts} 次重连...`),setTimeout((()=>{this.connect()}),this.reconnectInterval))}sendMessage(e){if(!this.socket||this.socket.readyState!==WebSocket.OPEN)return console.error("WebSocket未连接，无法发送消息"),!1;try{const t="string"==typeof e?e:JSON.stringify(e);return this.socket.send(t),!0}catch(t){return console.error("发送WebSocket消息失败:",t),!1}}disconnect(){this.socket&&(this.socket.close(),this.socket=null,this.isConnected=!1)}on(e,t){this.listeners.has(e)||this.listeners.set(e,[]),this.listeners.get(e).push(t)}off(e,t){if(!this.listeners.has(e))return;const o=this.listeners.get(e),s=o.indexOf(t);-1!==s&&o.splice(s,1)}triggerEvent(e,t){if(!this.listeners.has(e))return;this.listeners.get(e).forEach((o=>{try{o(t)}catch(s){console.error(`执行${e}事件监听器出错:`,s)}}))}};var de=c({state:{accessToken:localStorage.getItem("accessToken")||"",refreshToken:localStorage.getItem("refreshToken")||"",userInfo:JSON.parse(localStorage.getItem("userInfo"))||null},mutations:{SET_TOKEN(e,t){e.accessToken=t.accessToken,e.refreshToken=t.refreshToken,localStorage.setItem("accessToken",e.accessToken),localStorage.setItem("refreshToken",e.refreshToken)},CLEAR_AUTH(e){e.accessToken="",e.refreshToken="",e.userInfo=null,localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("userInfo"),me.disconnect()},SET_USER_INFO(e,t){e.userInfo=t,localStorage.setItem("userInfo",JSON.stringify(e.userInfo))}},actions:{async login({commit:e},t){try{const s=await ue.login(t);if(console.log("响应",s),200!==s.code)throw new Error(s.message||"登录失败");e("SET_TOKEN",s.data),console.log("登录成功，准备连接WebSocket"),console.log("accessToken值:",s.data.accessToken),s.data.accessToken?me.connect(s.data.accessToken):console.error("无法连接WebSocket: accessToken不存在");try{const t=await ue.getAdmin();if(200!==t.code)return console.error("获取用户信息失败:",t.message),Promise.resolve("登录成功，但获取用户信息失败");e("SET_USER_INFO",t.data)}catch(o){return console.error("获取用户信息出错:",o),Promise.resolve("登录成功，但获取用户信息失败")}return Promise.resolve("登录成功")}catch(s){return e("CLEAR_AUTH"),Promise.reject(s)}},logout({commit:e}){e("CLEAR_AUTH")}},getters:{isAuthenticated:e=>!!e.accessToken}});const ue={register:e=>le.post("/admin/register",e),login:e=>le.post("/admin/login",e),getAdmin:()=>le.get("/admin/getAdmin")};le.interceptors.request.use((e=>{const t=localStorage.getItem("accessToken");return t&&(e.headers.Authorization=`Bearer ${t}`),e}),(e=>(console.log("请求错误",e),Promise.reject(e)))),function(e){let t=!1,o=[];const s=(e="登录已过期，请重新登录")=>{l.confirm(e,"提示",{confirmButtonText:"重新登录",type:"warning",showCancelButton:!1,center:!0}).then((()=>{de.dispatch("logout"),window.location.href="/login"}))};e.interceptors.response.use((e=>{const{data:t}=e;if(200!=t.code){const e=new Error(t.message||"业务逻辑错误");return e.name="BusinessError",e.data=t,e.code=t.code,Promise.reject(e)}return t}),(async r=>{const n=r.config;if(!r.response)return Promise.reject(r);const a=r.response.status;if(console.log("statusCode",a),(401===a||500===a)&&!n._retry){if(n._retry=!0,console.log("isRefreshing",t),t)return new Promise((t=>{o.push((o=>{n.headers.Authorization=`Bearer ${o}`,t(e(n))}))}));t=!0;try{const a=localStorage.getItem("refreshToken");if(!a)return s(),Promise.reject(r);const i=new FormData;i.append("refreshToken",a);const c=await le.post("/admin/refreshToken",i);console.log("刷新token响应:",c);const l=c.data||c;console.log("响应数据:",l);const m=l.accessToken,d=l.refreshToken;if(!m)throw new Error("刷新token失败：未获取到新的accessToken");return localStorage.setItem("accessToken",m),d&&localStorage.setItem("refreshToken",d),n.headers.Authorization=`Bearer ${m}`,o.forEach((e=>e(m))),o=[],e(n)}catch(i){return s(),Promise.reject(i)}finally{t=!1}}return Promise.reject(r)}))}(le);var pe=c({state:{accessToken:localStorage.getItem("accessToken")||"",refreshToken:localStorage.getItem("refreshToken")||"",userInfo:JSON.parse(localStorage.getItem("userInfo"))||null},mutations:{SET_TOKEN(e,t){e.accessToken=t.accessToken,e.refreshToken=t.refreshToken,localStorage.setItem("accessToken",e.accessToken),localStorage.setItem("refreshToken",e.refreshToken)},CLEAR_AUTH(e){e.accessToken="",e.refreshToken="",e.userInfo=null,localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),localStorage.removeItem("userInfo"),me.disconnect()},SET_USER_INFO(e,t){e.userInfo=t,localStorage.setItem("userInfo",JSON.stringify(e.userInfo))}},actions:{async login({commit:e},t){try{const s=await ue.login(t);if(console.log("响应",s),200!==s.code)throw new Error(s.message||"登录失败");e("SET_TOKEN",s.data),console.log("登录成功，准备连接WebSocket"),console.log("accessToken值:",s.data.accessToken),s.data.accessToken?me.connect(s.data.accessToken):console.error("无法连接WebSocket: accessToken不存在");try{const t=await ue.getAdmin();if(200!==t.code)return console.error("获取用户信息失败:",t.message),Promise.resolve("登录成功，但获取用户信息失败");e("SET_USER_INFO",t.data)}catch(o){return console.error("获取用户信息出错:",o),Promise.resolve("登录成功，但获取用户信息失败")}return Promise.resolve("登录成功")}catch(s){return e("CLEAR_AUTH"),Promise.reject(s)}},logout({commit:e}){e("CLEAR_AUTH")}},getters:{isAuthenticated:e=>!!e.accessToken}});const he={class:"navbar"},_e={class:"left-panel"},fe={class:"system-title"},ge={class:"right-panel"},ke={class:"user-avatar"},Te={class:"user-name"};var ve=m({__name:"Navbar",setup(e){var t;const o=null==(t=d())?void 0:t.appContext.config.globalProperties.$global,s=JSON.parse(localStorage.getItem("userInfo")),r=(null==s?void 0:s.username)||"admin",n=u(),a=e=>{n.push(e)},i=async()=>{confirm("确定要退出吗？")&&(await pe.dispatch("logout"),n.replace("/login"))};return(e,t)=>{var s;const n=N,c=p,l=h,m=_,d=f,u=g,C=k;return T(),v("div",he,[E("div",_e,[S(n,{size:24},{default:b((()=>[S(y(I))])),_:1}),E("span",fe,A(null==(s=y(o))?void 0:s.system.name)+"管理系统",1)]),E("div",ge,[S(c,{content:"系统设置"},{default:b((()=>[S(n,{size:20},{default:b((()=>[S(y(O))])),_:1})])),_:1}),S(l,{value:5,class:"message-badge"},{default:b((()=>[S(c,{content:"消息中心"},{default:b((()=>[S(n,{size:20},{default:b((()=>[S(y(w))])),_:1})])),_:1})])),_:1}),S(C,null,{dropdown:b((()=>[S(u,null,{default:b((()=>[S(d,{onClick:t[0]||(t[0]=e=>a("/user/center"))},{default:b((()=>[S(n,null,{default:b((()=>[S(y(L))])),_:1}),P(" 个人中心 ")])),_:1}),S(d,{onClick:t[1]||(t[1]=e=>a("/log"))},{default:b((()=>[S(n,null,{default:b((()=>[S(y(R))])),_:1}),P(" 日志中心 ")])),_:1}),S(d,{divided:"",onClick:i},{default:b((()=>[S(n,null,{default:b((()=>[S(y(j))])),_:1}),P(" 退出登录 ")])),_:1})])),_:1})])),default:b((()=>[E("div",ke,[S(m,{size:32,src:"https://example.com/avatar.jpg"}),E("span",Te,A(y(r)),1)])])),_:1})])])}}});function Ee(){return le({url:"/admin/permission/tree",method:"get"})}function Se(e){return le({url:"/admin/permission/add",method:"post",data:e})}function be(e){return le({url:"/admin/permission/update",method:"put",data:e})}function ye(e){return le({url:`/admin/permission/delete/${e}`,method:"delete"})}ve.__scopeId="data-v-5b6dffae";let Ie=[];function Ae(e){return!e||Ie.includes(e)}var Oe=m({__name:"DynamicSidebar",setup(e){const i=C([]),c={Setting:O,Shop:H,Notebook:J,Monitor:K,Document:R,User:L,Message:w,ShoppingCart:q,Connection:G,Box:Q,TrendCharts:X,HomeFilled:Y},l=e=>c[e]||O,m=x((()=>d(i.value))),d=e=>e&&0!==e.length?e.map((e=>{if(e.children&&e.children.length>0){const c=d(e.children);return i=((e,t)=>{for(var o in t||(t={}))r.call(t,o)&&a(e,o,t[o]);if(s)for(var o of s(t))n.call(t,o)&&a(e,o,t[o]);return e})({},e),t(i,o({children:c}))}var i;return e})).filter((e=>1===e.type?e.children&&e.children.length>0:2===e.type&&(!e.code||Ae(e.code)))):[],u=async()=>{try{const e=await le({url:"/admin/permission/user/tree",method:"get"});200===e.code&&(i.value=e.data||[])}catch(e){console.error("获取用户菜单失败:",e)}};return D((()=>{u()})),(e,t)=>{const o=N,s=V,r=W,n=U;return T(),$(n,{router:"","default-active":e.$route.path,collapse:!1,"unique-opened":"","background-color":"#304156","text-color":"#bfcbd9","active-text-color":"#409EFF"},{default:b((()=>[(T(!0),v(z,null,M(m.value,(e=>(T(),v(z,{key:e.id},[e.children&&e.children.length>0?(T(),$(r,{key:0,index:e.path||e.id.toString()},{title:b((()=>[e.icon?(T(),$(o,{key:0},{default:b((()=>[(T(),$(F(l(e.icon))))])),_:2},1024)):B("",!0),E("span",null,A(e.name),1)])),default:b((()=>[(T(!0),v(z,null,M(e.children,(e=>(T(),$(s,{key:e.id,index:e.path},{default:b((()=>[P(A(e.name),1)])),_:2},1032,["index"])))),128))])),_:2},1032,["index"])):e.path?(T(),$(s,{key:1,index:e.path},{default:b((()=>[e.icon?(T(),$(o,{key:0},{default:b((()=>[(T(),$(F(l(e.icon))))])),_:2},1024)):B("",!0),E("span",null,A(e.name),1)])),_:2},1032,["index"])):B("",!0)],64)))),128))])),_:1},8,["default-active"])}}});Oe.__scopeId="data-v-267b5488";var we=m({__name:"Index",setup:e=>(e,t)=>{const o=Z,s=ee,r=te("router-view"),n=oe,a=se;return T(),$(a,{class:"layout-container"},{default:b((()=>[S(o,{height:"60px"},{default:b((()=>[S(ve)])),_:1}),S(a,null,{default:b((()=>[S(s,{width:"220px"},{default:b((()=>[S(Oe)])),_:1}),S(n,{style:{padding:"5px"}},{default:b((()=>[S(r)])),_:1})])),_:1})])),_:1})}});we.__scopeId="data-v-33f51ce5";var Le=Object.freeze({__proto__:null,[Symbol.toStringTag]:"Module",default:we});const Pe={__name:"App",setup(e){const t=re(),o=x((()=>!t.meta.noLayout));return(e,t)=>{const s=te("router-view");return o.value?(T(),$(we,{key:0},{default:b((()=>[S(s)])),_:1})):(T(),$(s,{key:1}))}}},Re={},je=function(e,t){return t&&0!==t.length?Promise.all(t.map((e=>{if((e=`/${e}`)in Re)return;Re[e]=!0;const t=e.endsWith(".css"),o=t?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${o}`))return;const s=document.createElement("link");return s.rel=t?"stylesheet":"modulepreload",t||(s.as="script",s.crossOrigin=""),s.href=e,document.head.appendChild(s),t?new Promise(((e,t)=>{s.addEventListener("load",e),s.addEventListener("error",t)})):void 0}))).then((()=>e())):e()},Ne=[{path:"/",component:()=>je((()=>Promise.resolve().then((function(){return Le}))),void 0),children:[{path:"",component:()=>je((()=>import("./TabsView.436b9bbb.js")),["assets/TabsView.436b9bbb.js","assets/TabsView.c3702853.css","assets/vendor.db8f8423.js","assets/vendor.f9422048.css"]),meta:{noLayout:!0},children:[{path:"/welcome",component:()=>je((()=>import("./Index.aa4dd8a3.js")),["assets/Index.aa4dd8a3.js","assets/Index.ce5cde9d.css","assets/el-card.40323e49.css","assets/el-col.40817b40.css","assets/vendor.db8f8423.js","assets/vendor.f9422048.css"]),meta:{title:"欢迎"}},{path:"/SettledConfig/list",component:()=>je((()=>import("./List.5faddc4d.js")),["assets/List.5faddc4d.js","assets/List.c01b5947.css","assets/el-overlay.7e3bbad5.css","assets/el-switch.5c2a972f.css","assets/el-divider.0e977bc9.css","assets/el-input.7b677563.css","assets/el-input-number.65f9b1ef.css","assets/el-checkbox.583d4ee9.css","assets/el-select.e30c78a8.css","assets/el-pagination.52030e07.css","assets/el-table-column.4d034af6.css","assets/el-form-item.fd530480.css","assets/vendor.db8f8423.js","assets/vendor.f9422048.css","assets/ActionBar.136c7dba.js","assets/RefreshButton.7e4e5928.css","assets/RefreshButton.54bbaca3.js"]),meta:{title:"配置列表",permission:"settled:config:list"}},{path:"/SettledConfig/memberRecord",component:()=>je((()=>import("./MemberRecord.6ba85395.js")),["assets/MemberRecord.6ba85395.js","assets/MemberRecord.85ee488d.css","assets/el-loading.6eef1391.css","assets/el-overlay.7e3bbad5.css","assets/el-input.7b677563.css","assets/el-input-number.65f9b1ef.css","assets/el-checkbox.583d4ee9.css","assets/el-select.e30c78a8.css","assets/el-pagination.52030e07.css","assets/el-table-column.4d034af6.css","assets/el-form-item.fd530480.css","assets/el-date-picker.c37c0117.css","assets/vendor.db8f8423.js","assets/vendor.f9422048.css","assets/RefreshButton.54bbaca3.js","assets/RefreshButton.7e4e5928.css"]),meta:{title:"会员开通记录",permission:"settled:member:record"}},{path:"/user/list",component:()=>je((()=>import("./List.c6b7ed11.js")),["assets/List.c6b7ed11.js","assets/List.41ac4dca.css","assets/el-loading.6eef1391.css","assets/el-overlay.7e3bbad5.css","assets/el-form-item.fd530480.css","assets/el-checkbox.583d4ee9.css","assets/el-select.e30c78a8.css","assets/el-pagination.52030e07.css","assets/el-input.7b677563.css","assets/el-table-column.4d034af6.css","assets/vendor.db8f8423.js","assets/vendor.f9422048.css","assets/role.0416e54a.js"]),meta:{title:"用户列表",permission:"user:list:view"}},{path:"/user/role",component:()=>je((()=>import("./Role.a7f1394e.js")),["assets/Role.a7f1394e.js","assets/Role.4d15255f.css","assets/el-loading.6eef1391.css","assets/el-popconfirm.ef94d602.css","assets/el-checkbox.583d4ee9.css","assets/el-overlay.7e3bbad5.css","assets/el-form-item.fd530480.css","assets/el-switch.5c2a972f.css","assets/el-input.7b677563.css","assets/el-card.40323e49.css","assets/el-table-column.4d034af6.css","assets/el-popover.103bfbf9.css","assets/vendor.db8f8423.js","assets/vendor.f9422048.css","assets/role.0416e54a.js"]),meta:{title:"角色管理",permission:"user:role:manage"}},{path:"/user/permission",component:()=>je((()=>import("./Permission.634d5104.js")),["assets/Permission.634d5104.js","assets/Permission.32ea98f8.css","assets/el-overlay.7e3bbad5.css","assets/el-form-item.fd530480.css","assets/el-switch.5c2a972f.css","assets/el-input.7b677563.css","assets/el-input-number.65f9b1ef.css","assets/el-checkbox.583d4ee9.css","assets/el-select.e30c78a8.css","assets/el-card.40323e49.css","assets/el-popconfirm.ef94d602.css","assets/el-popover.103bfbf9.css","assets/vendor.db8f8423.js","assets/vendor.f9422048.css"]),meta:{title:"权限管理",permission:"user:permission:manage"}},{path:"/invitation/list",component:()=>je((()=>import("./index.21531f06.js")),["assets/index.21531f06.js","assets/index.80f06c37.css","assets/el-loading.6eef1391.css","assets/el-overlay.7e3bbad5.css","assets/el-card.40323e49.css","assets/el-pagination.52030e07.css","assets/el-checkbox.583d4ee9.css","assets/el-select.e30c78a8.css","assets/el-input.7b677563.css","assets/el-table-column.4d034af6.css","assets/el-form-item.fd530480.css","assets/vendor.db8f8423.js","assets/vendor.f9422048.css"]),meta:{title:"邀请列表",permission:"invitation:list:view"}},{path:"/shop/list",component:()=>je((()=>import("./index.441eb34a.js")),["assets/index.441eb34a.js","assets/index.cba02d07.css","assets/el-loading.6eef1391.css","assets/el-overlay.7e3bbad5.css","assets/el-radio.ac8ebaf8.css","assets/el-pagination.52030e07.css","assets/el-checkbox.583d4ee9.css","assets/el-select.e30c78a8.css","assets/el-input.7b677563.css","assets/el-table-column.4d034af6.css","assets/el-switch.5c2a972f.css","assets/el-form-item.fd530480.css","assets/vendor.db8f8423.js","assets/vendor.f9422048.css","assets/RefreshButton.54bbaca3.js","assets/RefreshButton.7e4e5928.css"]),meta:{title:"店铺列表",permission:"shop:list:view"}},{path:"/book/selection/center",component:()=>je((()=>import("./index.d7c942c1.js")),["assets/index.d7c942c1.js","assets/index.3ab89b99.css","assets/el-loading.6eef1391.css","assets/el-checkbox.583d4ee9.css","assets/el-overlay.7e3bbad5.css","assets/el-form-item.fd530480.css","assets/el-progress.e041cf4a.css","assets/el-col.40817b40.css","assets/el-pagination.52030e07.css","assets/el-select.e30c78a8.css","assets/el-input.7b677563.css","assets/el-table-column.4d034af6.css","assets/el-popover.103bfbf9.css","assets/el-input-number.65f9b1ef.css","assets/el-date-picker.c37c0117.css","assets/vendor.db8f8423.js","assets/vendor.f9422048.css","assets/RefreshButton.54bbaca3.js","assets/RefreshButton.7e4e5928.css"]),meta:{title:"选品中心",permission:"book:selection:view"}},{path:"/warehouse/depot/list",component:()=>je((()=>import("./List.1a9d5064.js")),["assets/List.1a9d5064.js","assets/List.105316c5.css","assets/el-loading.6eef1391.css","assets/el-checkbox.583d4ee9.css","assets/el-select.e30c78a8.css","assets/el-overlay.7e3bbad5.css","assets/el-form-item.fd530480.css","assets/el-radio.ac8ebaf8.css","assets/el-pagination.52030e07.css","assets/el-input.7b677563.css","assets/el-table-column.4d034af6.css","assets/vendor.db8f8423.js","assets/vendor.f9422048.css"]),meta:{title:"货区管理",permission:"warehouse:depot:view"}},{path:"/tools/cards/list",component:()=>je((()=>import("./List.48e9c14b.js")),["assets/List.48e9c14b.js","assets/List.cfff731a.css","assets/el-loading.6eef1391.css","assets/el-overlay.7e3bbad5.css","assets/el-pagination.52030e07.css","assets/el-checkbox.583d4ee9.css","assets/el-select.e30c78a8.css","assets/el-input.7b677563.css","assets/el-table-column.4d034af6.css","assets/el-form-item.fd530480.css","assets/el-date-picker.c37c0117.css","assets/el-input-number.65f9b1ef.css","assets/vendor.db8f8423.js","assets/vendor.f9422048.css","assets/cards.ff03880b.js","assets/RefreshButton.54bbaca3.js","assets/RefreshButton.7e4e5928.css","assets/ActionBar.136c7dba.js"]),meta:{title:"卡密列表",permission:"cards:list:view"}},{path:"/tools/cards/activeCardsList",component:()=>je((()=>import("./ActiveCardsList.777c57ab.js")),["assets/ActiveCardsList.777c57ab.js","assets/ActiveCardsList.d2072a49.css","assets/el-loading.6eef1391.css","assets/el-pagination.52030e07.css","assets/el-checkbox.583d4ee9.css","assets/el-select.e30c78a8.css","assets/el-input.7b677563.css","assets/el-table-column.4d034af6.css","assets/vendor.db8f8423.js","assets/vendor.f9422048.css","assets/ActionBar.136c7dba.js","assets/RefreshButton.7e4e5928.css","assets/RefreshButton.54bbaca3.js","assets/cards.ff03880b.js"]),meta:{title:"活跃卡密列表",permission:"cards:active:view"}},{path:"/examine/violation/list",component:()=>je((()=>import("./List.2b3637a4.js")),["assets/List.2b3637a4.js","assets/List.b375e6dd.css","assets/el-loading.6eef1391.css","assets/el-checkbox.583d4ee9.css","assets/el-overlay.7e3bbad5.css","assets/el-pagination.52030e07.css","assets/el-select.e30c78a8.css","assets/el-input.7b677563.css","assets/el-table-column.4d034af6.css","assets/el-form-item.fd530480.css","assets/vendor.db8f8423.js","assets/vendor.f9422048.css","assets/RefreshButton.54bbaca3.js","assets/RefreshButton.7e4e5928.css","assets/ActionBar.136c7dba.js"]),meta:{title:"违规列表"}},{path:"/log/runningLog/list",component:()=>je((()=>import("./List.07c3d716.js")),["assets/List.07c3d716.js","assets/List.31eb8f1d.css","assets/el-loading.6eef1391.css","assets/el-overlay.7e3bbad5.css","assets/el-input.7b677563.css","assets/el-pagination.52030e07.css","assets/el-checkbox.583d4ee9.css","assets/el-select.e30c78a8.css","assets/el-table-column.4d034af6.css","assets/vendor.db8f8423.js","assets/vendor.f9422048.css","assets/RefreshButton.54bbaca3.js","assets/RefreshButton.7e4e5928.css","assets/ActionBar.136c7dba.js"]),meta:{title:"运行日志",permission:"log:running:view"}},{path:"/task/list",component:()=>je((()=>import("./List.b9d9a1b2.js")),["assets/List.b9d9a1b2.js","assets/List.97c1ba35.css","assets/el-overlay.7e3bbad5.css","assets/el-descriptions-item.11af55f0.css","assets/el-progress.e041cf4a.css","assets/el-pagination.52030e07.css","assets/el-checkbox.583d4ee9.css","assets/el-select.e30c78a8.css","assets/el-input.7b677563.css","assets/el-card.40323e49.css","assets/el-table-column.4d034af6.css","assets/vendor.db8f8423.js","assets/vendor.f9422048.css"]),meta:{title:"任务列表",permission:"task:list:view"}},{path:"/useModule/vas/list",component:()=>je((()=>import("./List.537bedea.js")),["assets/List.537bedea.js","assets/List.880ae485.css","assets/el-loading.6eef1391.css","assets/el-overlay.7e3bbad5.css","assets/el-descriptions-item.11af55f0.css","assets/el-pagination.52030e07.css","assets/el-checkbox.583d4ee9.css","assets/el-select.e30c78a8.css","assets/el-input.7b677563.css","assets/el-table-column.4d034af6.css","assets/el-form-item.fd530480.css","assets/vendor.db8f8423.js","assets/vendor.f9422048.css","assets/ActionBar.136c7dba.js","assets/RefreshButton.7e4e5928.css","assets/RefreshButton.54bbaca3.js"]),meta:{title:"服务列表",permission:"vas:list:view"}},{path:"/monitor/dashboard",component:()=>je((()=>import("./Dashboard.35542cd7.js")),["assets/Dashboard.35542cd7.js","assets/Dashboard.456bc7a2.css","assets/vendor.db8f8423.js","assets/vendor.f9422048.css"]),meta:{title:"监控大屏",permission:"monitor:dashboard:view"}}]}]},{path:"/login",component:()=>je((()=>import("./Index.0d2b37ea.js")),["assets/Index.0d2b37ea.js","assets/Index.932715bd.css","assets/el-form-item.fd530480.css","assets/el-input.7b677563.css","assets/el-divider.0e977bc9.css","assets/vendor.db8f8423.js","assets/vendor.f9422048.css"]),meta:{noLayout:!0,public:!0}},{path:"/redirectUrl",component:()=>je((()=>import("./index.096171b8.js")),["assets/index.096171b8.js","assets/index.80fdf597.css","assets/el-form-item.fd530480.css","assets/el-input.7b677563.css","assets/vendor.db8f8423.js","assets/vendor.f9422048.css"]),meta:{noLayout:!0,title:"用户注册",public:!0}}],Ce=ne({history:ae(),routes:Ne});Ce.beforeEach(((e,t,o)=>{if(e.meta&&e.meta.permission){const t=Ie,s=e.meta.permission;t.includes(s)?o():o("/403")}else o()}));const xe={mounted(e,t){const{value:o}=t;if(o){let t=!1;t=Array.isArray(o)?!(s=o)||0===s.length||s.some((e=>Ae(e))):Ae(o),t||e.parentNode&&e.parentNode.removeChild(e)}var s}},De={mounted(e,t){const{value:o}=t;if(o&&Array.isArray(o)){o.every((e=>Ae(e)))||e.parentNode&&e.parentNode.removeChild(e)}}},Ve=ie(Pe);Ve.directive("permission",xe),Ve.directive("permission-all",De),Ve.use(pe),Ve.use(Ce),Ve.use(ce),async function(){try{const e=await le({url:"/admin/permission/user/codes",method:"get"});200===e.code&&(Ie=e.data.filter((e=>e&&""!==e.trim())))}catch(e){console.error("获取用户权限失败:",e)}}().then((()=>{Ve.mount("#app")}));export{Se as a,ye as d,Ee as g,le as i,de as s,be as u};
