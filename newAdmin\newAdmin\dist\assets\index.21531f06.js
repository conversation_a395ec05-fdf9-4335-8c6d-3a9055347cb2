import{d as e,A as a,ab as l,C as t,ac as n,ad as r,a6 as o,ae as i,af as d,ai as u,aj as s,a8 as p,ap as c,au as v,o as m,k as g,m as h,w as b,v as f,av as w,H as I,l as y,t as _,aq as C,a9 as z,aa as j}from"./vendor.db8f8423.js";/* empty css                   *//* empty css                   *//* empty css                *//* empty css                      *//* empty css                    *//* empty css                  *//* empty css                 *//* empty css                        *//* empty css                     */import{i as V}from"./index.2f9d9633.js";const k=async(e={})=>{const a=new URLSearchParams;e.pageNum&&a.append("pageNum",e.pageNum),e.pageSize&&a.append("pageSize",e.pageSize),e.userId&&a.append("userId",e.userId),e.phonenumber&&a.append("phonenumber",e.phonenumber);const l=`/inviteCodes/erp/getInviteCodes?${a.toString()}`,t=await V.get(l);return console.log("API原始响应:",t),t},S=e=>V.get(`/inviteCodes/inviteRelations/inviter?inviterId=${e}`),U={class:"invitation-container"},x=(e=>(z("data-v-c6a650d2"),e=e(),j(),e))((()=>y("div",{class:"card-header"},[y("span",null,"邀请码管理")],-1))),N={class:"invite-url-container"},D={class:"pagination-container"};var A=e({__name:"index",setup(e){const z=a([]),j=a([]),V=a(!1),A=a(!1),P=a(!1),$=a(null),L=a(0),R=a(10),T=a(1),q=l({userId:"",phonenumber:""}),H=e=>{if(!e)return"";return new Date(e).toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}).replace(/\//g,"-")},B=async()=>{V.value=!0;try{const e={pageNum:T.value,pageSize:R.value};q.userId&&(e.userId=q.userId),q.phonenumber&&(e.phonenumber=q.phonenumber);const a=await k(e);a&&a.data&&200===a.code?(z.value=a.data.list||[],L.value=a.data.total||0,R.value=a.data.pageSize||10,T.value=a.data.pageNum||1,console.log("设置到inviteCodes的数据:",z.value)):(console.error("无法解析API返回数据:",a),C.error("获取邀请码列表失败"),z.value=[],L.value=0)}catch(e){console.error("获取邀请码列表出错:",e),C.error("获取邀请码列表出错"),z.value=[],L.value=0}finally{V.value=!1}},E=()=>{T.value=1,B()},F=()=>{q.userId="",q.phonenumber="",T.value=1,B()},G=e=>{R.value=e,T.value=1,B()},J=e=>{T.value=e,B()};return t((()=>{B()})),(e,a)=>{const l=n,t=r,k=o,B=i,K=d,M=u,O=s,Q=p,W=c,X=v;return m(),g("div",U,[h(Q,{class:"box-card"},{header:b((()=>[x])),default:b((()=>[h(B,{inline:!0,class:"search-form"},{default:b((()=>[h(t,{label:"用户ID"},{default:b((()=>[h(l,{modelValue:q.userId,"onUpdate:modelValue":a[0]||(a[0]=e=>q.userId=e),placeholder:"请输入用户ID",clearable:""},null,8,["modelValue"])])),_:1}),h(t,{label:"手机号码"},{default:b((()=>[h(l,{modelValue:q.phonenumber,"onUpdate:modelValue":a[1]||(a[1]=e=>q.phonenumber=e),placeholder:"请输入手机号码",clearable:""},null,8,["modelValue"])])),_:1}),h(t,null,{default:b((()=>[h(k,{type:"primary",onClick:E},{default:b((()=>[f("查询")])),_:1}),h(k,{onClick:F},{default:b((()=>[f("重置")])),_:1})])),_:1})])),_:1}),w((m(),I(M,{data:z.value,stripe:"",style:{width:"100%"},border:""},{default:b((()=>[h(K,{prop:"id",label:"ID",width:"70",align:"center"}),h(K,{prop:"userId",label:"用户ID","min-width":"180",align:"center"}),h(K,{prop:"phonenumber",label:"手机号码","min-width":"120",align:"center"}),h(K,{prop:"code",label:"邀请码",width:"120",align:"center"}),h(K,{label:"邀请链接","min-width":"300",align:"center"},{default:b((e=>[y("div",N,[h(l,{modelValue:e.row.inviteUrl,"onUpdate:modelValue":a=>e.row.inviteUrl=a,readonly:"",size:"small",class:"invite-url-input"},null,8,["modelValue","onUpdate:modelValue"]),h(k,{type:"primary",size:"small",onClick:a=>{return l=e.row.inviteUrl,void navigator.clipboard.writeText(l).then((()=>{C.success("邀请链接已复制到剪贴板")})).catch((()=>{C.error("复制失败，请手动复制")}));var l},class:"copy-btn"},{default:b((()=>[f(" 复制 ")])),_:2},1032,["onClick"])])])),_:1}),h(K,{label:"创建时间",width:"160",align:"center"},{default:b((e=>[f(_(H(e.row.createdAt)),1)])),_:1}),h(K,{label:"操作",width:"120",align:"center"},{default:b((e=>[h(k,{type:"primary",size:"small",onClick:a=>(async e=>{console.log("inviterId:",e),$.value=e,P.value=!0,A.value=!0;try{const a=await S(e);a&&a.data&&200===a.code?(j.value=a.data||[],console.log("处理后的被邀请人数据:",j.value),0===j.value.length&&C.info("该用户暂无邀请记录")):(console.warn("获取被邀请人列表返回异常结构:",a),j.value=[])}catch(a){console.error("获取被邀请人列表出错:",a),j.value=[]}finally{A.value=!1}})(e.row.userId)},{default:b((()=>[f(" 查看被邀请人 ")])),_:2},1032,["onClick"])])),_:1})])),_:1},8,["data"])),[[X,V.value]]),y("div",D,[h(O,{background:"",layout:"total, sizes, prev, pager, next, jumper",total:L.value,"page-size":R.value,"current-page":T.value,"page-sizes":[10,20,50,100],onSizeChange:G,onCurrentChange:J},null,8,["total","page-size","current-page"])])])),_:1}),h(W,{modelValue:P.value,"onUpdate:modelValue":a[2]||(a[2]=e=>P.value=e),title:`邀请总数: ${j.value.length}人`,width:"50%"},{default:b((()=>[w((m(),I(M,{data:j.value,stripe:"",style:{width:"100%"},border:""},{default:b((()=>[h(K,{prop:"phonenumber",label:"手机号码","min-width":"150",align:"center"}),h(K,{label:"邀请时间","min-width":"150",align:"center"},{default:b((e=>[f(_(H(e.row.inviteTime)),1)])),_:1})])),_:1},8,["data"])),[[X,A.value]])])),_:1},8,["modelValue","title"])])}}});A.__scopeId="data-v-c6a650d2";export{A as default};
