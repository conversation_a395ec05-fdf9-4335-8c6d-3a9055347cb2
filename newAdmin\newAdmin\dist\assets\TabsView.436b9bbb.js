import{d as a,Z as e,u as l,A as t,C as u,a2 as s,a3 as o,a4 as n,W as v,o as c,k as d,m as i,w as p,J as m,I as r,l as h,H as f,K as b,a5 as w}from"./vendor.db8f8423.js";const _={class:"tabs-container"},P={class:"tab-content"};var V=a({__name:"TabsView",setup(a){const V=e(),k=l(),y=t(V.path),I=t([]),x=t([]);u((()=>{I.value.some((a=>"/welcome"===a.path))||I.value.push({title:"欢迎页",path:"/welcome"})})),s(V,(a=>{var e;const l=a.fullPath,t=(null==(e=a.meta.title)?void 0:e.toString())||"异常页";if("/welcome"===l)return;I.value.find((a=>a.path===l))||(I.value.push({title:t,path:l}),x.value.includes(l)||x.value.push(l)),y.value=l}),{immediate:!0}),s(y,(a=>{a&&a!==V.fullPath&&k.push(a)}));const C=a=>{if("/welcome"===a)return;const e=I.value.findIndex((e=>e.path===a));if(-1!==e&&(x.value=x.value.filter((e=>e!==a)),I.value.splice(e,1),a===V.fullPath)){const a=I.value[e]||I.value[e-1];y.value=(null==a?void 0:a.path)||"/welcome"}};return(a,e)=>{const l=o,t=n,u=v("router-view");return c(),d("div",_,[i(t,{modelValue:y.value,"onUpdate:modelValue":e[0]||(e[0]=a=>y.value=a),type:"card",closable:"",onTabRemove:C},{default:p((()=>[(c(!0),d(m,null,r(I.value,(a=>(c(),f(l,{key:a.path,label:a.title,name:a.path,closable:"/welcome"!==a.path},null,8,["label","name","closable"])))),128))])),_:1},8,["modelValue"]),h("div",P,[i(u,null,{default:p((({Component:e})=>[(c(),f(w,{include:x.value},[(c(),f(b(e),{key:a.$route.fullPath}))],1032,["include"]))])),_:1})])])}}});V.__scopeId="data-v-c370b4d0";export{V as default};
