import{A as a,C as e,a6 as t,aR as l,af as s,ah as r,b as o,ai as d,a8 as n,aj as i,aS as c,aT as u,aU as v,ap as p,o as g,k as f,m,w as y,l as h,v as b,H as k,t as w,n as _,x as A,aV as C,J as j,I as x,aW as z,aX as I,a9 as S,aa as T,aq as $,E as N,z as U}from"./vendor.db8f8423.js";/* empty css                   *//* empty css                             *//* empty css                    *//* empty css                      *//* empty css                    *//* empty css                  *//* empty css                 *//* empty css                *//* empty css                        */import{i as L}from"./index.2f9d9633.js";const R=(a=0,e=10)=>L.get(`/task/getRunningTasks?pageSize=${a}&pageNum=${e}`),V=a=>L.get(`/task/stopTask?taskId=${a}`),B=a=>L.get(`/task/logsList?id=${a}`),D=a=>L.get(`/task/logsMsg?id=${a}`),O=(a,e)=>L.get(`/task/logsDetailList/${a}/${e}`),P=a=>L.get(`/task/downloadLogs/${a}`,{responseType:"blob"});const E={class:"task-list-container"},J={class:"card-header"},M=(a=>(S("data-v-f35b1f38"),a=a(),T(),a))((()=>h("span",null,"任务列表",-1))),q={key:0,class:"loading-content"},H={key:1,class:"empty-content"},W={class:"operation-buttons"},X={class:"pagination-container"},F={class:"task-msg"},G={class:"logs-container"},K={class:"logs-message-section"},Q={class:"logs-message-content"},Y={key:0,class:"loading-text"},Z={key:1,class:"logs-text"},aa={class:"refresh-btn-container"},ea={class:"logs-table-section"},ta={class:"operation-buttons"},la={class:"dialog-footer"},sa={__name:"List",setup(S){const T=a([]),L=a(!1),sa=a([]),ra=a(!1),oa=a({}),da=a(0),na=a(1),ia=a(10),ca=a(!1),ua=a(!1),va=a([]),pa=a(""),ga=a(""),fa=a(!1),ma=a(!1),ya=a([]),ha=a(null),ba=async()=>{var a;L.value=!0;try{const e=na.value-1,t=await R(e,ia.value);console.log("API响应数据:",t),200===t.code?t.data?t.data.data&&Array.isArray(t.data.data)?(T.value=t.data.data||[],da.value=t.data.total||0):t.data.list&&Array.isArray(t.data.list)?(T.value=t.data.list||[],da.value=t.data.total||0):t.data.records&&Array.isArray(t.data.records)?(T.value=t.data.records||[],da.value=t.data.total||0):Array.isArray(t.data)?(T.value=t.data,da.value=t.data.length):(T.value=[],da.value=0,console.warn("未能识别的数据格式:",t.data)):(T.value=[],da.value=0):$.error((null==(a=t.data)?void 0:a.message)||"获取任务列表失败")}catch(e){console.error("获取任务列表出错:",e),$.error("获取任务列表失败: "+(e.message||"未知错误"))}finally{L.value=!1}},ka=a=>{na.value=a,ba()},wa=a=>{ia.value=a,na.value=1,ba()},_a=()=>{ba()},Aa=a=>({0:"未开始",1:"执行中",2:"已完成",3:"已中止",4:"执行失败"}[a]||"未知状态"),Ca=a=>{if(!a||!a.msg)return 0;try{const e=a.msg.match(/总执行数据：(\d+)条/),t=a.msg.match(/已执行条数：(\d+)条/);if(e&&t){const a=parseInt(e[1]),l=parseInt(t[1]);if(a>0)return Math.floor(l/a*100)}return 0}catch(e){return 0}},ja=async()=>{ua.value=!0;try{const[a,e]=await Promise.all([B(ga.value),D(ga.value)]);console.log("日志列表响应:",a),console.log("日志消息响应:",e),200===a.code?Array.isArray(a.data)?va.value=a.data:a.data&&Array.isArray(a.data.data)?va.value=a.data.data:va.value=[]:(va.value=[],$.error("获取日志列表失败: "+(a.message||"未知错误"))),200===e.code?"string"==typeof e.data?pa.value=e.data:e.data&&"object"==typeof e.data?pa.value=e.data.message||e.data.msg||JSON.stringify(e.data):pa.value="暂无日志消息":(pa.value="获取日志消息失败",$.error("获取日志消息失败: "+(e.message||"未知错误")))}catch(a){console.error("获取日志数据出错:",a),$.error("获取日志数据失败: "+(a.message||"未知错误")),va.value=[],pa.value="获取日志数据失败"}finally{ua.value=!1}},xa=()=>{ja()},za=a=>{if(!a)return["暂无日志消息"];const e=a.split(/\r?\n|\r/).filter((a=>""!==a.trim()));return 0===e.length?["暂无日志消息"]:e};return e((()=>{ba()})),(a,e)=>{const S=t,R=l,B=s,D=r,Ia=U,Sa=o,Ta=d,$a=n,Na=i,Ua=c,La=u,Ra=v,Va=p;return g(),f("div",E,[m($a,null,{header:y((()=>[h("div",J,[M,m(S,{type:"primary",size:"small",onClick:_a},{default:y((()=>[b("刷新")])),_:1})])])),default:y((()=>[L.value?(g(),f("div",q,[m(R,{rows:5,animated:""})])):0===T.value.length?(g(),f("div",H," 暂无运行中的任务 ")):(g(),k(Ta,{key:2,data:T.value,border:"",style:{width:"100%"},height:"500","max-height":"500","header-cell-style":{backgroundColor:"#f5f7fa",color:"#606266",textAlign:"center"}},{default:y((()=>[m(B,{prop:"id",label:"任务ID",width:"180",align:"center"}),m(B,{prop:"fileName",label:"文件名称",align:"center"}),m(B,{prop:"shopNames",label:"店铺名称",align:"center"}),m(B,{prop:"dataNum",label:"数据总数",width:"100",align:"center"}),m(B,{prop:"taskStatus",label:"状态",width:"100",align:"center"},{default:y((a=>{return[m(D,{type:(e=a.row.taskStatus,{0:"info",1:"success",2:"success",3:"warning",4:"danger"}[e]||"info")},{default:y((()=>[b(w(Aa(a.row.taskStatus)),1)])),_:2},1032,["type"])];var e})),_:1}),m(B,{prop:"createTime",label:"创建时间",width:"160",align:"center"}),m(B,{label:"操作",width:"180",fixed:"right",align:"center"},{default:y((a=>[h("div",W,[m(Sa,{content:"查看日志",placement:"top","hide-after":1500},{default:y((()=>[m(S,{type:"primary",size:"small",onClick:e=>(async a=>{ga.value=a,ca.value=!0,await ja()})(a.row.id),circle:""},{default:y((()=>[m(Ia,null,{default:y((()=>[m(_(A))])),_:1})])),_:2},1032,["onClick"])])),_:2},1024),m(Sa,{content:"停止任务",placement:"top","hide-after":1500},{default:y((()=>[m(S,{type:"danger",size:"small",onClick:e=>(async a=>{console.log("taskId",a);try{await N.confirm("确定要停止任务吗？","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),sa.value.push(a);const e=await V(a);200===e.code?($.success("任务已成功停止"),ba()):$.error(e.data.message||"停止任务失败")}catch(e){"cancel"!==e&&(console.error("停止任务出错:",e),$.error("停止任务失败: "+(e.message||"未知错误")))}finally{sa.value=sa.value.filter((e=>e!==a))}})(a.row.id),loading:sa.value.includes(a.row.id),disabled:"1"!==a.row.taskStatus,circle:""},{default:y((()=>[m(Ia,null,{default:y((()=>[m(_(C))])),_:1})])),_:2},1032,["onClick","loading","disabled"])])),_:2},1024)])])),_:1})])),_:1},8,["data"]))])),_:1}),h("div",X,[m(Na,{"current-page":na.value,"onUpdate:currentPage":e[0]||(e[0]=a=>na.value=a),"page-size":ia.value,"onUpdate:pageSize":e[1]||(e[1]=a=>ia.value=a),"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",total:da.value,onSizeChange:wa,onCurrentChange:ka},null,8,["current-page","page-size","total"])]),m(Va,{modelValue:ra.value,"onUpdate:modelValue":e[2]||(e[2]=a=>ra.value=a),title:"任务详情",width:"500px"},{default:y((()=>[m(Ra,{column:1,border:""},{default:y((()=>[m(Ua,{label:"任务ID"},{default:y((()=>[b(w(oa.value.id),1)])),_:1}),m(Ua,{label:"文件名称"},{default:y((()=>[b(w(oa.value.fileName),1)])),_:1}),m(Ua,{label:"店铺名称"},{default:y((()=>[b(w(oa.value.shopNames),1)])),_:1}),m(Ua,{label:"数据总数"},{default:y((()=>[b(w(oa.value.dataNum),1)])),_:1}),m(Ua,{label:"创建时间"},{default:y((()=>[b(w(oa.value.createTime),1)])),_:1}),m(Ua,{label:"执行进度"},{default:y((()=>[m(La,{percentage:Ca(oa.value),status:"1"===oa.value.taskStatus?"success":"exception"},null,8,["percentage","status"])])),_:1}),m(Ua,{label:"执行消息"},{default:y((()=>[h("div",F,w(oa.value.msg),1)])),_:1})])),_:1})])),_:1},8,["modelValue"]),m(Va,{modelValue:ca.value,"onUpdate:modelValue":e[4]||(e[4]=a=>ca.value=a),title:"任务日志",width:"900px","close-on-click-modal":!1},{footer:y((()=>[h("div",la,[m(S,{onClick:e[3]||(e[3]=a=>ca.value=!1)},{default:y((()=>[b("关闭")])),_:1})])])),default:y((()=>[h("div",G,[h("div",K,[h("div",Q,[ua.value?(g(),f("div",Y,"加载中...")):(g(),f("div",Z,[(g(!0),f(j,null,x(za(pa.value),((a,e)=>(g(),f("div",{key:e,class:"log-line"},w(a),1)))),128))]))]),h("div",aa,[m(S,{type:"primary",size:"small",onClick:xa,loading:ua.value,icon:"Refresh"},{default:y((()=>[b(" 刷新 ")])),_:1},8,["loading"])])]),h("div",ea,[m(Ta,{data:va.value,border:"",style:{width:"100%"},"header-cell-style":{backgroundColor:"#f5f7fa",color:"#606266",textAlign:"center"}},{default:y((()=>[m(B,{prop:"shopName",label:"店铺名称",align:"center"}),m(B,{prop:"progress",label:"进度",width:"100",align:"center"},{default:y((a=>[b(w(a.row.progress)+"% ",1)])),_:1}),m(B,{label:"创建时间/修改时间",width:"300",align:"center"},{default:y((a=>[b(w(a.row.createTime)+" / "+w(a.row.updateTime),1)])),_:1}),m(B,{label:"操作",width:"150",align:"center"},{default:y((a=>[h("div",ta,[m(Sa,{content:"查看详情",placement:"top","hide-after":1500},{default:y((()=>[m(S,{type:"primary",size:"small",onClick:e=>(async a=>{try{ha.value=a,ma.value=!0,fa.value=!0;const e=await O(a.taskId,a.shopId);console.log("详细日志响应:",e),200===e.code?e.data&&"object"==typeof e.data?Array.isArray(e.data.data)?ya.value=e.data.data:Array.isArray(e.data.list)?ya.value=e.data.list:Array.isArray(e.data.records)?ya.value=e.data.records:Array.isArray(e.data)?ya.value=e.data:(ya.value=[],console.warn("未能识别的详细日志数据格式:",e.data)):ya.value=[]:(ya.value=[],$.error("获取详细日志失败: "+(e.message||"未知错误")))}catch(e){console.error("获取详细日志出错:",e),$.error("获取详细日志失败: "+(e.message||"未知错误")),ya.value=[]}finally{ma.value=!1}})(a.row),circle:""},{default:y((()=>[m(Ia,null,{default:y((()=>[m(_(z))])),_:1})])),_:2},1032,["onClick"])])),_:2},1024),m(Sa,{content:"下载日志",placement:"top","hide-after":1500},{default:y((()=>[m(S,{type:"success",size:"small",onClick:e=>(async a=>{try{const e=`${a.taskId}${a.shopId}.txt`,t=await P(e),l=new Blob([t.data]),s=window.URL.createObjectURL(l),r=document.createElement("a");r.href=s,r.download=e,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(s),$.success("文件下载成功")}catch(e){console.error("下载日志文件出错:",e),$.error("下载日志文件失败: "+(e.message||"未知错误"))}})(a.row),circle:""},{default:y((()=>[m(Ia,null,{default:y((()=>[m(_(I))])),_:1})])),_:2},1032,["onClick"])])),_:2},1024)])])),_:1})])),_:1},8,["data"])])])])),_:1},8,["modelValue"])])}},__scopeId:"data-v-f35b1f38"};export{sa as default};
